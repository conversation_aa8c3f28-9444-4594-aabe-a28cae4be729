# 🚀 WIDDX AI - Free Setup Guide

## ⚡ Quick Start (5 minutes)

### 1. **Required API Keys** (Only 2 needed!)
```env
# Add these to your .env file
DEEPSEEK_API_KEY=your_deepseek_key_here
GEMINI_API_KEY=your_gemini_key_here

# Enable free alternatives
WIDDX_USE_FREE_ALTERNATIVES=true
```

### 2. **Get Free API Keys**

#### DeepSeek API (~$2/month)
1. Go to [platform.deepseek.com](https://platform.deepseek.com)
2. Sign up and get $5 free credit
3. Copy your API key to `.env`

#### Gemini API (Free tier)
1. Go to [ai.google.dev](https://ai.google.dev)
2. Get free API key (generous free tier)
3. Copy your API key to `.env`

### 3. **Test Everything**
```bash
# Test only free features
php artisan widdx:test-free

# Test all features (including paid ones if configured)
php artisan widdx:test-features
```

### 4. **Start Using**
```bash
php artisan serve
npm run dev
```

Visit `http://localhost:8000` and enjoy!

## 🆓 What Works for Free

### ✅ **Completely Free** (No API keys needed)
- 🔍 **Search** - DuckDuckGo API
- 🎤 **Voice** - Browser TTS/STT
- 📊 **Trends** - Generated topics

### 💰 **Very Cheap** (DeepSeek + Gemini)
- 🧠 **Think Mode** - Step-by-step reasoning
- 🎨 **Image Descriptions** - Detailed descriptions
- 📄 **Document Analysis** - Text processing
- 👁️ **Vision** - Image understanding
- 🔬 **Deep Search** - Multi-source analysis

## 🎯 Usage Examples

### Chat Interface
```
User: "ابحث عن آخر أخبار الذكاء الاصطناعي"
WIDDX: [Shows search results from DuckDuckGo]

User: "ارسم منظر طبيعي جميل"
WIDDX: [Generates detailed description + ASCII art]

User: [Clicks voice button]
WIDDX: [Activates browser-based voice controls]
```

### API Usage
```bash
# Free search
curl -X POST localhost:8000/api/features/search \
  -d '{"query": "AI news", "use_free": true}'

# Free image description
curl -X POST localhost:8000/api/features/generate-image \
  -d '{"prompt": "sunset", "use_free": true}'

# Free voice widget
curl localhost:8000/api/features/voice-widget?language=ar
```

## 🔧 Troubleshooting

### Common Issues

**1. "DeepSeek API error"**
```bash
# Check your API key
curl -H "Authorization: Bearer YOUR_DEEPSEEK_KEY" \
     https://api.deepseek.com/v1/models
```

**2. "Gemini API error"**
```bash
# Verify Gemini key
curl "https://generativelanguage.googleapis.com/v1/models?key=YOUR_GEMINI_KEY"
```

**3. "Search not working"**
```bash
# Test DuckDuckGo directly
curl "https://api.duckduckgo.com/?q=test&format=json"
```

**4. "Voice not working"**
- Make sure you're using a modern browser
- Allow microphone permissions
- Check browser console for errors

### Performance Tips

**1. Enable caching**
```env
CACHE_DRIVER=file
WIDDX_SEARCH_CACHE_MINUTES=30
```

**2. Optimize for your language**
```env
WIDDX_DEFAULT_LANGUAGE=arabic
WIDDX_DEFAULT_LANGUAGE_CODE=ar
```

**3. Adjust limits**
```env
WIDDX_SEARCH_MAX_RESULTS=5
WIDDX_THINK_MAX_STEPS=5
```

## 📊 Cost Breakdown

| Service | Monthly Usage | Cost |
|---------|---------------|------|
| DeepSeek | 1M tokens | ~$0.14 |
| Gemini | Free tier | $0.00 |
| DuckDuckGo | Unlimited | $0.00 |
| Browser APIs | Unlimited | $0.00 |
| **Total** | | **~$0.14/month** |

Compare to paid alternatives: **$50+/month**

## 🎉 You're Ready!

Your WIDDX AI is now running with:
- ✅ Real-time search
- ✅ Image descriptions
- ✅ Voice controls
- ✅ Think mode
- ✅ Document analysis
- ✅ Vision capabilities

All for less than **$2/month**! 🎊

## 🚀 Next Steps

1. **Customize** - Edit `config/widdx.php` for your needs
2. **Extend** - Add your own free services
3. **Deploy** - Put it online for others to use
4. **Upgrade** - Add paid services when you need more

**Enjoy your free AI assistant!** 🤖✨
