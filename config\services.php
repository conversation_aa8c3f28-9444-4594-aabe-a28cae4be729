<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Free Search Services
    |--------------------------------------------------------------------------
    */
    'free_search' => [
        'duckduckgo' => [
            'base_url' => 'https://api.duckduckgo.com',
            'enabled' => true,
        ],
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'deepseek' => [
        'api_key' => env('DEEPSEEK_API_KEY'),
        'base_url' => env('DEEPSEEK_BASE_URL', 'https://api.deepseek.com'),
        'timeout' => env('DEEPSEEK_TIMEOUT', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Live Search Services Configuration
    |--------------------------------------------------------------------------
    */
    'search' => [
        'google' => [
            'api_key' => env('GOOGLE_SEARCH_API_KEY'),
            'search_engine_id' => env('GOOGLE_SEARCH_ENGINE_ID'),
            'base_url' => 'https://www.googleapis.com/customsearch/v1',
        ],
        'bing' => [
            'api_key' => env('BING_SEARCH_API_KEY'),
            'base_url' => 'https://api.bing.microsoft.com/v7.0/search',
        ],
        'serp' => [
            'api_key' => env('SERP_API_KEY'),
            'base_url' => 'https://serpapi.com/search',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Generation Services Configuration
    |--------------------------------------------------------------------------
    */
    'image_generation' => [
        'openai' => [
            'api_key' => env('OPENAI_API_KEY'),
            'base_url' => 'https://api.openai.com/v1',
        ],
        'stability' => [
            'api_key' => env('STABILITY_API_KEY'),
            'base_url' => 'https://api.stability.ai/v1',
        ],
        'midjourney' => [
            'api_key' => env('MIDJOURNEY_API_KEY'),
            'base_url' => env('MIDJOURNEY_BASE_URL'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Voice Services Configuration
    |--------------------------------------------------------------------------
    */
    'voice' => [
        'elevenlabs' => [
            'api_key' => env('ELEVENLABS_API_KEY'),
            'base_url' => 'https://api.elevenlabs.io/v1',
        ],
        'openai' => [
            'api_key' => env('OPENAI_API_KEY'),
            'base_url' => 'https://api.openai.com/v1',
        ],
        'azure' => [
            'api_key' => env('AZURE_SPEECH_API_KEY'),
            'region' => env('AZURE_SPEECH_REGION'),
            'base_url' => 'https://{region}.tts.speech.microsoft.com',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Document Analysis Services Configuration
    |--------------------------------------------------------------------------
    */
    'document_analysis' => [
        'azure' => [
            'api_key' => env('AZURE_DOCUMENT_API_KEY'),
            'endpoint' => env('AZURE_DOCUMENT_ENDPOINT'),
        ],
        'google' => [
            'credentials_path' => env('GOOGLE_CLOUD_CREDENTIALS_PATH'),
            'project_id' => env('GOOGLE_CLOUD_PROJECT_ID'),
        ],
    ],

    'gemini' => [
        'api_key' => env('GEMINI_API_KEY'),
        'base_url' => env('GEMINI_BASE_URL', 'https://generativelanguage.googleapis.com'),
        'timeout' => env('GEMINI_TIMEOUT', 30),
    ],

];
