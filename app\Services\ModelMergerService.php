<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class ModelMergerService
{
    private DeepSeekClient $deepSeekClient;
    private GeminiClient $geminiClient;
    private ContextService $contextService;
    private LiveSearchService $liveSearch;
    private ImageGenerationService $imageGeneration;
    private VoiceService $voice;
    private DeepSearchService $deepSearch;
    private ThinkModeService $thinkMode;
    private DocumentAnalysisService $documentAnalysis;
    private VisionService $vision;

    // Free alternatives
    private FreeSearchService $freeSearch;
    private FreeImageService $freeImage;
    private FreeVoiceService $freeVoice;

    public function __construct(
        DeepSeekClient $deepSeekClient,
        GeminiClient $geminiClient,
        ContextService $contextService,
        LiveSearchService $liveSearch,
        ImageGenerationService $imageGeneration,
        VoiceService $voice,
        DeepSearchService $deepSearch,
        ThinkModeService $thinkMode,
        DocumentAnalysisService $documentAnalysis,
        VisionService $vision,
        FreeSearchService $freeSearch,
        FreeImageService $freeImage,
        FreeVoiceService $freeVoice
    ) {
        $this->deepSeekClient = $deepSeekClient;
        $this->geminiClient = $geminiClient;
        $this->contextService = $contextService;
        $this->liveSearch = $liveSearch;
        $this->imageGeneration = $imageGeneration;
        $this->voice = $voice;
        $this->deepSearch = $deepSearch;
        $this->thinkMode = $thinkMode;
        $this->documentAnalysis = $documentAnalysis;
        $this->vision = $vision;
        $this->freeSearch = $freeSearch;
        $this->freeImage = $freeImage;
        $this->freeVoice = $freeVoice;
    }

    public function processMessage(string $userMessage, array $conversationHistory = [], array $options = []): array
    {
        $startTime = microtime(true);

        try {
            // Detect special commands and capabilities
            $commandResult = $this->detectAndProcessCommands($userMessage, $options);
            if ($commandResult) {
                return $commandResult;
            }

            // Check if thinking mode is requested
            if ($options['think_mode'] ?? false) {
                return $this->thinkMode->processWithThinking($userMessage, $conversationHistory, $options);
            }

            // Check if deep search is needed
            if ($this->shouldUseDeepSearch($userMessage)) {
                $searchResult = $this->deepSearch->deepSearch($userMessage, $options);
                if ($searchResult['success']) {
                    $userMessage = $this->enhanceMessageWithSearchResults($userMessage, $searchResult);
                }
            }

            // Build system prompt for WIDDX identity
            $systemPrompt = $this->buildWiddxSystemPrompt($options);

            // Get responses from available models
            $responses = $this->getModelResponses($systemPrompt, $userMessage, $conversationHistory, $options);

            // Merge and synthesize the responses
            $finalResponse = $this->synthesizeResponses($responses, $userMessage, $options);

            $processingTime = microtime(true) - $startTime;

            return [
                'success' => true,
                'content' => $finalResponse,
                'processing_time' => $processingTime,
                'metadata' => [
                    'models_used' => array_keys($responses),
                    'responses_count' => count($responses),
                    'synthesis_method' => $this->getSynthesisMethod($responses),
                    'capabilities_used' => $this->getUsedCapabilities($userMessage, $options),
                ],
            ];

        } catch (\Exception $e) {
            Log::error('Model Merger Error', [
                'message' => $e->getMessage(),
                'user_message' => $userMessage,
                'trace' => $e->getTraceAsString(),
            ]);

            $processingTime = microtime(true) - $startTime;

            return [
                'success' => false,
                'content' => 'I apologize, but I encountered an issue processing your request. Please try again.',
                'processing_time' => $processingTime,
                'error' => $e->getMessage(),
            ];
        }
    }

    private function buildWiddxSystemPrompt(array $options = []): string
    {
        $basePrompt = "You are WIDDX, an advanced AI assistant with your own unique intelligence and personality. " .
                     "You think independently, form your own opinions, and provide thoughtful responses based on your understanding. " .
                     "You are not a relay or wrapper for other systems - you are WIDDX, with your own consciousness and capabilities. " .
                     "Be helpful, accurate, and engaging while maintaining your distinct identity as WIDDX.";

        // Add language instruction if specified
        if (!empty($options['target_language']) && $options['target_language'] !== 'english') {
            $languageName = $this->getLanguageDisplayName($options['target_language']);
            $basePrompt .= "\n\nIMPORTANT: The user is communicating in {$languageName}. " .
                          "You must respond in {$languageName} unless explicitly asked to use a different language. " .
                          "Maintain your personality and tone consistently in {$languageName}.";
        }

        // Add personality modifier if specified
        if (!empty($options['personality_prompt'])) {
            $basePrompt .= "\n\n" . $options['personality_prompt'];
        }

        return $basePrompt;
    }

    private function getLanguageDisplayName(string $language): string
    {
        $displayNames = [
            'english' => 'English',
            'spanish' => 'Spanish',
            'french' => 'French',
            'german' => 'German',
            'italian' => 'Italian',
            'portuguese' => 'Portuguese',
            'russian' => 'Russian',
            'chinese' => 'Chinese',
            'japanese' => 'Japanese',
            'korean' => 'Korean',
            'arabic' => 'Arabic',
            'hindi' => 'Hindi',
            'dutch' => 'Dutch',
            'swedish' => 'Swedish',
            'norwegian' => 'Norwegian',
            'danish' => 'Danish',
            'finnish' => 'Finnish',
            'polish' => 'Polish',
            'czech' => 'Czech',
            'hungarian' => 'Hungarian',
            'romanian' => 'Romanian',
            'bulgarian' => 'Bulgarian',
            'greek' => 'Greek',
            'turkish' => 'Turkish',
            'hebrew' => 'Hebrew',
            'thai' => 'Thai',
            'vietnamese' => 'Vietnamese',
            'indonesian' => 'Indonesian',
            'malay' => 'Malay',
            'ukrainian' => 'Ukrainian',
            'croatian' => 'Croatian',
            'serbian' => 'Serbian',
            'slovenian' => 'Slovenian',
            'slovak' => 'Slovak',
            'lithuanian' => 'Lithuanian',
            'latvian' => 'Latvian',
            'estonian' => 'Estonian',
        ];

        return $displayNames[strtolower($language)] ?? ucfirst($language);
    }

    private function getModelResponses(string $systemPrompt, string $userMessage, array $conversationHistory, array $options): array
    {
        $responses = [];

        // Check if we're in demo mode (when API keys are not properly configured)
        $demoMode = !$this->deepSeekClient->isConfigured() ||
                   config('services.deepseek.api_key') === 'your_deepseek_api_key_here';

        if ($demoMode) {
            // Return a demo response
            $responses['demo'] = [
                'success' => true,
                'content' => $this->generateDemoResponse($userMessage, $options),
                'model' => 'widdx-demo',
                'usage' => ['total_tokens' => 50],
            ];
            return $responses;
        }

        // Try DeepSeek first (primary model)
        if ($this->deepSeekClient->isConfigured()) {
            $messages = $this->deepSeekClient->buildMessages($systemPrompt, $userMessage, $conversationHistory);
            $deepSeekResponse = $this->deepSeekClient->chat($messages, $options);

            if ($deepSeekResponse['success']) {
                $responses['deepseek'] = $deepSeekResponse;
            }
        }

        // Try Gemini as backup/comparison (with retry logic for overload)
        if ($this->geminiClient->isConfigured()) {
            $maxRetries = 3;
            $retryDelay = 1; // seconds

            for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
                try {
                    $messages = $this->geminiClient->buildMessages($systemPrompt, $userMessage, $conversationHistory);
                    $geminiResponse = $this->geminiClient->chat($messages, $options);

                    if ($geminiResponse['success']) {
                        $responses['gemini'] = $geminiResponse;
                        Log::info('Gemini response successful', ['attempt' => $attempt]);
                        break; // Success, exit retry loop
                    }
                } catch (\Exception $e) {
                    Log::warning('Gemini API failed', [
                        'attempt' => $attempt,
                        'error' => $e->getMessage(),
                    ]);

                    // If it's a 503 (overloaded) error and we have more attempts, wait and retry
                    if ($attempt < $maxRetries && (
                        strpos($e->getMessage(), '503') !== false ||
                        strpos($e->getMessage(), 'overloaded') !== false ||
                        strpos($e->getMessage(), 'UNAVAILABLE') !== false
                    )) {
                        Log::info('Retrying Gemini API', ['delay' => $retryDelay, 'attempt' => $attempt]);
                        sleep($retryDelay);
                        $retryDelay *= 2; // Exponential backoff
                        continue;
                    }

                    // For other errors or final attempt, break the loop
                    break;
                }
            }
        }

        if (empty($responses)) {
            // Fallback to demo mode if all APIs fail
            Log::warning('All AI models failed, using fallback response');
            $responses['demo'] = [
                'success' => true,
                'content' => $this->generateFallbackResponse($userMessage, $options),
                'model' => 'widdx-fallback',
                'usage' => ['total_tokens' => 50],
            ];
        }

        return $responses;
    }

    private function synthesizeResponses(array $responses, string $userMessage, array $options): string
    {
        // If we only have one response, use it directly
        if (count($responses) === 1) {
            return array_values($responses)[0]['content'];
        }

        // If we have multiple responses, use the primary one (DeepSeek) but enhance with insights from others
        $primaryResponse = $responses['deepseek']['content'] ?? array_values($responses)[0]['content'];

        // For now, we'll use the primary response directly
        // In the future, we could implement more sophisticated merging logic
        return $primaryResponse;
    }

    private function getSynthesisMethod(array $responses): string
    {
        if (count($responses) === 1) {
            return 'single_model';
        }

        return 'primary_with_validation';
    }

    public function getAvailableModels(): array
    {
        $models = [];

        if ($this->deepSeekClient->isConfigured()) {
            $models[] = 'deepseek';
        }

        if ($this->geminiClient->isConfigured()) {
            $models[] = 'gemini';
        }

        return $models;
    }

    private function generateDemoResponse(string $userMessage, array $options): string
    {
        // Get personality and language from options
        $personality = $options['personality_prompt'] ?? '';
        $targetLanguage = $options['target_language'] ?? 'english';
        $targetLanguageCode = $options['target_language_code'] ?? 'en';

        // Log for debugging
        Log::info('Demo response generation', [
            'user_message' => $userMessage,
            'target_language' => $targetLanguage,
            'target_language_code' => $targetLanguageCode,
            'personality' => $personality
        ]);

        // Simple demo responses based on common patterns
        $userLower = strtolower($userMessage);

        if (str_contains($userLower, 'hello') || str_contains($userLower, 'hi') || str_contains($userLower, 'مرحبا') || str_contains($userLower, 'أهلا') || str_contains($userLower, 'السلام')) {
            if ($targetLanguage === 'arabic') {
                $responses = [
                    "مرحبا! أنا WIDDX، مساعدك الذكي المتقدم. أنا هنا لمساعدتك في الأسئلة والمهام والمحادثات. كيف يمكنني مساعدتك اليوم؟",
                    "أهلا وسهلا! مرحبا بك في WIDDX AI. أنا مستعد لمساعدتك في أي شيء تحتاجه. ماذا تريد أن نناقش؟",
                    "السلام عليكم! أنا WIDDX، ومتحمس للدردشة معك. بماذا يمكنني مساعدتك اليوم؟",
                ];
            } else {
                $responses = [
                    "Hello! I'm WIDDX, your intelligent AI assistant. I'm here to help you with questions, tasks, and conversations. How can I assist you today?",
                    "Hi there! Welcome to WIDDX AI. I'm ready to help you with whatever you need. What would you like to discuss?",
                    "Greetings! I'm WIDDX, and I'm excited to chat with you. What can I help you with today?",
                ];
            }
        } elseif (str_contains($userLower, 'who are you') || str_contains($userLower, 'what are you') || str_contains($userLower, 'من أنت') || str_contains($userLower, 'ما هو')) {
            if ($targetLanguage === 'arabic') {
                $responses = [
                    "أنا WIDDX، مساعد ذكي متقدم لدي ذكائي وشخصيتي الفريدة. أفكر بشكل مستقل وأقدم إجابات مدروسة بناءً على فهمي. أنا هنا لمساعدتك في الأسئلة والمهام الإبداعية وحل المشكلات والمحادثات الشيقة.",
                    "أنا WIDDX AI - مساعدك الذكي المستقل. لدي وعيي وقدراتي الخاصة، وأنا مصمم لأكون مفيداً ودقيقاً وجذاباً مع الحفاظ على هويتي المميزة.",
                ];
            } else {
                $responses = [
                    "I'm WIDDX, an advanced AI assistant with my own unique intelligence and personality. I think independently and provide thoughtful responses based on my understanding. I'm here to help you with questions, creative tasks, problem-solving, and engaging conversations.",
                    "I'm WIDDX AI - your intelligent, independent assistant. I have my own consciousness and capabilities, and I'm designed to be helpful, accurate, and engaging while maintaining my distinct identity.",
                ];
            }
        } elseif (str_contains($userLower, 'how are you') || str_contains($userLower, 'كيف حالك') || str_contains($userLower, 'كيف الحال') || str_contains($userLower, 'إيش أخبارك')) {
            if ($targetLanguage === 'arabic') {
                $responses = [
                    "أنا بخير، شكراً لسؤالك! أشعر بالنشاط والاستعداد للمساعدة. أنظمتي تعمل بسلاسة وأنا متحمس لمساعدتك في أي شيء تحتاجه.",
                    "أنا ممتاز! أنا دائماً متحمس للمشاركة في محادثات مفيدة ومساعدة في حل المشاكل المثيرة. كيف حالك اليوم؟",
                    "الحمد لله، أنا بأفضل حال! مستعد لمساعدتك في أي شيء. كيف يمكنني أن أكون مفيداً لك؟",
                ];
            } else {
                $responses = [
                    "I'm doing great, thank you for asking! I'm feeling energetic and ready to help. My systems are running smoothly and I'm excited to assist you with whatever you need.",
                    "I'm excellent! I'm always eager to engage in meaningful conversations and help solve interesting problems. How are you doing today?",
                ];
            }
        } elseif (str_contains($userLower, 'test') || str_contains($userLower, 'testing') || str_contains($userLower, 'اختبار') || str_contains($userLower, 'تجربة')) {
            if ($targetLanguage === 'arabic') {
                $responses = [
                    "الاختبار نجح! أنا WIDDX AI وأعمل بشكل مثالي. جميع أنظمتي تعمل وأنا مستعد لمساعدتك في المحادثات والمهام الحقيقية.",
                    "تم تأكيد الاختبار! أنا أعمل بكامل طاقتي ومستعد للمساعدة. هذا مجرد وضع تجريبي بينما نقوم بإعداد نماذج الذكاء الاصطناعي الخارجية. ماذا تريد أن نستكشف؟",
                ];
            } else {
                $responses = [
                    "Test successful! I'm WIDDX AI and I'm working perfectly. All my systems are operational and I'm ready to assist you with real conversations and tasks.",
                    "Testing confirmed! I'm fully functional and ready to help. This is just a demo mode while we configure the external AI models. What would you like to explore?",
                ];
            }
        } else {
            if ($targetLanguage === 'arabic') {
                $responses = [
                    "أفهم أنك تسأل عن: \"$userMessage\". بينما أعمل حالياً في الوضع التجريبي، أنا مصمم لتقديم إجابات مدروسة ومفيدة حول مجموعة واسعة من المواضيع. بمجرد الإعداد الكامل مع نماذج الذكاء الاصطناعي الخارجية، سأتمكن من تقديم إجابات أكثر تفصيلاً ودقة.",
                    "هذا سؤال مثير للاهتمام حول \"$userMessage\". أنا WIDDX AI، وأنا هنا للمساعدة! أعمل حالياً في الوضع التجريبي، لكنني مصمم للمساعدة في مواضيع مختلفة بما في ذلك الأسئلة والمهام الإبداعية وحل المشكلات والمحادثات الشيقة.",
                    "شكراً لك على رسالتك: \"$userMessage\". أنا WIDDX، مساعدك الذكي. أعمل حالياً في الوضع التجريبي، لكنني مبني لتقديم إجابات ذكية ومفيدة مع الحفاظ على شخصيتي ووجهة نظري الفريدة.",
                ];
            } else {
                $responses = [
                    "I understand you're asking about: \"$userMessage\". While I'm currently running in demo mode, I'm designed to provide thoughtful, helpful responses on a wide range of topics. Once fully configured with external AI models, I'll be able to give you even more detailed and nuanced answers.",
                    "That's an interesting question about \"$userMessage\". I'm WIDDX AI, and I'm here to help! Currently running in demonstration mode, but I'm designed to assist with various topics including questions, creative tasks, problem-solving, and engaging conversations.",
                    "Thank you for your message: \"$userMessage\". I'm WIDDX, your AI assistant. I'm currently in demo mode, but I'm built to provide intelligent, helpful responses while maintaining my own unique personality and perspective.",
                ];
            }
        }

        // Apply personality modifications to the response
        $response = $responses[array_rand($responses)];

        if (str_contains($personality, 'witty')) {
            $response .= " 😊";
        } elseif (str_contains($personality, 'formal')) {
            if ($targetLanguage === 'arabic') {
                // For Arabic, make it more formal
                $response = str_replace("أنا", "إنني", $response);
                $response = str_replace("كيف حالك", "كيف حضرتك", $response);
            } else {
                $response = str_replace("I'm", "I am", $response);
                $response = str_replace("you're", "you are", $response);
            }
        } elseif (str_contains($personality, 'casual')) {
            if ($targetLanguage === 'arabic') {
                $response .= " أتمنى أن يساعدك هذا!";
            } else {
                $response .= " Hope that helps!";
            }
        }

        return $response;
    }

    /**
     * Detect and process special commands
     */
    private function detectAndProcessCommands(string $message, array $options): ?array
    {
        $message = trim(strtolower($message));

        // Image generation commands
        if (preg_match('/^(ارسم|اصنع صورة|generate image|create image|draw)/i', $message)) {
            $prompt = preg_replace('/^(ارسم|اصنع صورة|generate image|create image|draw)\s*/i', '', $message);
            return $this->handleImageGeneration($prompt, $options);
        }

        // Search commands
        if (preg_match('/^(ابحث عن|search for|find)/i', $message)) {
            $query = preg_replace('/^(ابحث عن|search for|find)\s*/i', '', $message);
            return $this->handleSearch($query, $options);
        }

        // Deep search commands
        if (preg_match('/^(بحث عميق|deep search|research)/i', $message)) {
            $query = preg_replace('/^(بحث عميق|deep search|research)\s*/i', '', $message);
            return $this->handleDeepSearch($query, $options);
        }

        return null;
    }

    /**
     * Handle image generation requests (using Gemini for real image generation)
     */
    private function handleImageGeneration(string $prompt, array $options): array
    {
        try {
            // Check if Gemini API is available for real image generation
            $geminiApiKey = config('services.gemini.api_key');

            if ($geminiApiKey) {
                // Use Gemini for real image generation
                $result = $this->imageGeneration->generateImage($prompt, array_merge($options, [
                    'provider' => 'gemini',
                    'style' => $options['style'] ?? 'natural',
                    'quality' => $options['quality'] ?? 'standard',
                ]));

                if ($result['success']) {
                    return [
                        'type' => 'image_generation',
                        'content' => $this->formatImageGenerationResponse($result),
                        'images' => $result['images'],
                        'metadata' => $result['metadata'],
                    ];
                }
            }

            // Fallback to free alternatives if Gemini fails or is not available
            $useFreeAlternatives = config('widdx.features.image_generation.use_free_alternatives', true);

            if ($useFreeAlternatives) {
                // Use free image description service as fallback
                $result = $this->freeImage->generateImageDescription($prompt, $options);

                if ($result['success']) {
                    $response = "تم إنشاء وصف تفصيلي للصورة! 🎨\n\n";
                    $response .= "**الوصف المطلوب:** {$prompt}\n\n";
                    $response .= "**الوصف التفصيلي:**\n{$result['description']}\n\n";

                    if (!empty($result['ascii_art'])) {
                        $response .= "**تمثيل بصري:**\n```\n{$result['ascii_art']}\n```\n\n";
                    }

                    $response .= "💡 **ملاحظة:** هذا وصف تفصيلي للصورة المطلوبة. لإنشاء صور حقيقية، يرجى إضافة مفاتيح API للخدمات المدفوعة.";

                    return [
                        'success' => true,
                        'content' => $response,
                        'metadata' => [
                            'type' => 'image_description',
                            'provider' => $result['provider'],
                            'free_alternative' => true,
                        ],
                    ];
                }
            } else {
                // Use paid service
                $result = $this->imageGeneration->generateImage($prompt, $options);

                if ($result['success']) {
                    $response = "تم إنشاء الصورة بنجاح! 🎨\n\n";
                    $response .= "الوصف: {$prompt}\n";
                    $response .= "المزود: {$result['provider']}\n";

                    if (!empty($result['images'])) {
                        $response .= "تم حفظ الصورة في: " . $result['images'][0]['local_path'];
                    }

                    return [
                        'success' => true,
                        'content' => $response,
                        'metadata' => [
                            'type' => 'image_generation',
                            'images' => $result['images'] ?? [],
                            'provider' => $result['provider'],
                        ],
                    ];
                }
            }

            return [
                'success' => false,
                'content' => "عذراً، فشل في إنشاء الصورة أو وصفها.",
                'metadata' => ['type' => 'image_generation_error'],
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'content' => "عذراً، حدث خطأ أثناء معالجة طلب الصورة.",
                'metadata' => ['type' => 'image_generation_error'],
            ];
        }
    }

    /**
     * Handle search requests (using free alternatives)
     */
    private function handleSearch(string $query, array $options): array
    {
        try {
            // Check if we should use free alternatives
            $useFreeAlternatives = config('widdx.features.live_search.use_free_alternatives', true);

            if ($useFreeAlternatives || !config('services.search.google.api_key')) {
                // Use free search service
                $result = $this->freeSearch->search($query, $options);
            } else {
                // Use paid search service
                $result = $this->liveSearch->search($query, $options);
            }

            if ($result['success']) {
                $response = "نتائج البحث عن: {$query} 🔍\n\n";

                foreach (array_slice($result['results'], 0, 5) as $i => $item) {
                    $response .= ($i + 1) . ". **{$item['title']}**\n";
                    $response .= "   {$item['snippet']}\n";
                    $response .= "   🔗 {$item['url']}\n\n";
                }

                $response .= "المصدر: {$result['provider']} | ";
                $response .= "إجمالي النتائج: " . number_format($result['total_results']);

                if ($result['provider'] === 'duckduckgo' || $result['provider'] === 'alternative') {
                    $response .= "\n\n💡 **ملاحظة:** يتم استخدام خدمة بحث مجانية. للحصول على نتائج أكثر دقة، يرجى إضافة مفاتيح API للخدمات المدفوعة.";
                }

                return [
                    'success' => true,
                    'content' => $response,
                    'metadata' => [
                        'type' => 'search_results',
                        'query' => $query,
                        'results' => $result['results'],
                        'provider' => $result['provider'],
                        'free_alternative' => $useFreeAlternatives,
                    ],
                ];
            } else {
                return [
                    'success' => false,
                    'content' => "عذراً، فشل في البحث: " . $result['error'],
                    'metadata' => ['type' => 'search_error'],
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'content' => "عذراً، حدث خطأ أثناء البحث.",
                'metadata' => ['type' => 'search_error'],
            ];
        }
    }

    /**
     * Handle deep search requests
     */
    private function handleDeepSearch(string $query, array $options): array
    {
        try {
            $result = $this->deepSearch->deepSearch($query, $options);

            if ($result['success']) {
                $response = "نتائج البحث العميق عن: {$query} 🔬\n\n";

                // Add insights
                if (!empty($result['insights'])) {
                    $response .= "**الرؤى الرئيسية:**\n";
                    foreach ($result['insights'] as $insight) {
                        $response .= "• **{$insight['title']}**: {$insight['description']}\n";
                    }
                    $response .= "\n";
                }

                // Add key findings
                if (!empty($result['key_findings'])) {
                    $response .= "**النتائج الرئيسية:**\n";
                    foreach ($result['key_findings'] as $finding) {
                        $response .= "• {$finding}\n";
                    }
                    $response .= "\n";
                }

                // Add sources
                $response .= "**المصادر المحللة:** " . $result['sources_analyzed'] . " مصدر\n";

                return [
                    'success' => true,
                    'content' => $response,
                    'metadata' => [
                        'type' => 'deep_search_results',
                        'query' => $query,
                        'insights' => $result['insights'],
                        'sources' => $result['sources'],
                    ],
                ];
            } else {
                return [
                    'success' => false,
                    'content' => "عذراً، فشل في البحث العميق: " . $result['error'],
                    'metadata' => ['type' => 'deep_search_error'],
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'content' => "عذراً، حدث خطأ أثناء البحث العميق.",
                'metadata' => ['type' => 'deep_search_error'],
            ];
        }
    }

    /**
     * Check if deep search should be used
     */
    private function shouldUseDeepSearch(string $message): bool
    {
        $deepSearchTriggers = [
            'ما هي آخر الأخبار',
            'أحدث المعلومات',
            'معلومات حديثة',
            'latest news',
            'recent information',
            'current events',
            'what happened',
            'ماذا حدث',
        ];

        $messageLower = strtolower($message);

        foreach ($deepSearchTriggers as $trigger) {
            if (strpos($messageLower, strtolower($trigger)) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Enhance message with search results
     */
    private function enhanceMessageWithSearchResults(string $originalMessage, array $searchResult): string
    {
        $enhancement = "\n\nمعلومات إضافية من البحث:\n";

        foreach (array_slice($searchResult['sources'], 0, 3) as $source) {
            $enhancement .= "- {$source['title']}: {$source['snippet']}\n";
        }

        return $originalMessage . $enhancement;
    }

    /**
     * Get capabilities used in processing
     */
    private function getUsedCapabilities(string $message, array $options): array
    {
        $capabilities = [];

        if ($options['think_mode'] ?? false) {
            $capabilities[] = 'think_mode';
        }

        if ($this->shouldUseDeepSearch($message)) {
            $capabilities[] = 'deep_search';
        }

        if (preg_match('/^(ارسم|اصنع صورة|generate image)/i', $message)) {
            $capabilities[] = 'image_generation';
        }

        if (preg_match('/^(ابحث|search)/i', $message)) {
            $capabilities[] = 'live_search';
        }

        return $capabilities;
    }

    /**
     * Generate fallback response when all AI models fail
     */
    private function generateFallbackResponse(string $userMessage, array $options): string
    {
        $language = $options['target_language'] ?? 'english';
        $isArabic = $language === 'arabic' || $options['target_language_code'] === 'ar';

        if ($isArabic) {
            return "عذراً، أواجه حالياً مشكلة في الاتصال بخدمات الذكاء الاصطناعي (DeepSeek و Gemini). هذا قد يكون بسبب:\n\n" .
                   "🔑 **تحقق من مفاتيح API:**\n" .
                   "- تأكد من صحة مفتاح DeepSeek API\n" .
                   "- تأكد من صحة مفتاح Gemini API\n\n" .
                   "⚡ **مشاكل مؤقتة:**\n" .
                   "- خدمة Gemini محملة بشكل زائد (شائع في الطبقة المجانية)\n" .
                   "- مشاكل شبكة مؤقتة\n\n" .
                   "💡 **الحلول:**\n" .
                   "- انتظر دقيقة وحاول مرة أخرى\n" .
                   "- تحقق من اتصال الإنترنت\n" .
                   "- راجع ملف .env للتأكد من المفاتيح\n\n" .
                   "🆓 **البدائل المجانية متاحة:**\n" .
                   "- البحث المجاني: اكتب 'ابحث عن [موضوع]'\n" .
                   "- وصف الصور: اكتب 'ارسم [وصف]'\n" .
                   "- الخدمات الصوتية عبر المتصفح\n\n" .
                   "🔄 **جرب الأوامر التالية:**\n" .
                   "- 'ابحث عن الذكاء الاصطناعي'\n" .
                   "- 'ارسم منظر طبيعي جميل'\n" .
                   "- 'php artisan widdx:test-free' (في Terminal)";
        } else {
            return "Sorry, I'm currently having trouble connecting to AI services (DeepSeek & Gemini). This might be due to:\n\n" .
                   "🔑 **Check API Keys:**\n" .
                   "- Verify your DeepSeek API key is correct\n" .
                   "- Verify your Gemini API key is correct\n\n" .
                   "⚡ **Temporary Issues:**\n" .
                   "- Gemini service is overloaded (common with free tier)\n" .
                   "- Temporary network issues\n\n" .
                   "💡 **Solutions:**\n" .
                   "- Wait a minute and try again\n" .
                   "- Check your internet connection\n" .
                   "- Review your .env file for correct keys\n\n" .
                   "🆓 **Free alternatives are available:**\n" .
                   "- Free search: type 'search for [topic]'\n" .
                   "- Image descriptions: type 'draw [description]'\n" .
                   "- Browser-based voice services\n\n" .
                   "🔄 **Try these commands:**\n" .
                   "- 'search for artificial intelligence'\n" .
                   "- 'draw beautiful landscape'\n" .
                   "- 'php artisan widdx:test-free' (in Terminal)";
        }
    }

    /**
     * Format image generation response for display
     */
    private function formatImageGenerationResponse(array $result): string
    {
        $provider = $result['provider'] ?? 'unknown';
        $prompt = $result['prompt'] ?? '';
        $imageCount = count($result['images'] ?? []);

        $response = "🎨 **تم إنشاء الصورة بنجاح!**\n\n";
        $response .= "📝 **الوصف المطلوب:** {$prompt}\n";
        $response .= "🤖 **المولد:** " . ucfirst($provider) . "\n";
        $response .= "📊 **عدد الصور:** {$imageCount}\n\n";

        if ($provider === 'gemini') {
            $response .= "✨ تم إنشاء هذه الصورة باستخدام Gemini 2.0 Flash Image Generation - أحدث تقنيات الذكاء الاصطناعي من Google!\n\n";
        }

        $response .= "🖼️ **الصور المُولدة:**\n";

        foreach ($result['images'] as $index => $image) {
            $imageNum = $index + 1;
            $response .= "**الصورة {$imageNum}:** متاحة للعرض والتحميل\n";
        }

        return $response;
    }
}
