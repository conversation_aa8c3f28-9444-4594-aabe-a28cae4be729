<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class VoiceService
{
    private array $providers;
    private int $timeout;

    public function __construct()
    {
        $this->providers = [
            'elevenlabs' => config('services.voice.elevenlabs'),
            'openai' => config('services.voice.openai'),
            'azure' => config('services.voice.azure'),
        ];
        $this->timeout = 60;
    }

    /**
     * Convert text to speech
     */
    public function textToSpeech(string $text, array $options = []): array
    {
        try {
            $provider = $options['provider'] ?? 'elevenlabs';
            $voice = $options['voice'] ?? 'default';
            $language = $options['language'] ?? 'ar';
            $speed = $options['speed'] ?? 1.0;
            $pitch = $options['pitch'] ?? 1.0;

            Log::info('Text-to-speech started', [
                'text_length' => strlen($text),
                'provider' => $provider,
                'voice' => $voice,
                'language' => $language,
            ]);

            $result = match($provider) {
                'elevenlabs' => $this->ttsElevenLabs($text, $voice, $options),
                'openai' => $this->ttsOpenAI($text, $voice, $options),
                'azure' => $this->ttsAzure($text, $voice, $language, $speed, $pitch),
                default => $this->ttsElevenLabs($text, $voice, $options),
            };

            Log::info('Text-to-speech completed', [
                'provider' => $provider,
                'success' => $result['success'],
                'audio_file' => $result['audio_file'] ?? null,
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Text-to-speech error', [
                'text' => substr($text, 0, 100),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'audio_file' => null,
            ];
        }
    }

    /**
     * Convert speech to text
     */
    public function speechToText(string $audioFilePath, array $options = []): array
    {
        try {
            $provider = $options['provider'] ?? 'openai';
            $language = $options['language'] ?? 'ar';

            Log::info('Speech-to-text started', [
                'audio_file' => $audioFilePath,
                'provider' => $provider,
                'language' => $language,
            ]);

            $result = match($provider) {
                'openai' => $this->sttOpenAI($audioFilePath, $language),
                'azure' => $this->sttAzure($audioFilePath, $language),
                default => $this->sttOpenAI($audioFilePath, $language),
            };

            Log::info('Speech-to-text completed', [
                'provider' => $provider,
                'success' => $result['success'],
                'text_length' => strlen($result['text'] ?? ''),
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Speech-to-text error', [
                'audio_file' => $audioFilePath,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'text' => '',
            ];
        }
    }

    /**
     * Text-to-speech using ElevenLabs
     */
    private function ttsElevenLabs(string $text, string $voice, array $options): array
    {
        $config = $this->providers['elevenlabs'];
        
        if (!$config['api_key']) {
            throw new \Exception('ElevenLabs API key not configured');
        }

        // Get available voices if voice is 'default'
        if ($voice === 'default') {
            $voice = $this->getDefaultVoice('elevenlabs', $options['language'] ?? 'ar');
        }

        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'xi-api-key' => $config['api_key'],
                'Content-Type' => 'application/json',
            ])
            ->post($config['base_url'] . "/text-to-speech/{$voice}", [
                'text' => $text,
                'model_id' => 'eleven_multilingual_v2',
                'voice_settings' => [
                    'stability' => $options['stability'] ?? 0.5,
                    'similarity_boost' => $options['similarity_boost'] ?? 0.75,
                    'style' => $options['style'] ?? 0.0,
                    'use_speaker_boost' => $options['use_speaker_boost'] ?? true,
                ],
            ]);

        if (!$response->successful()) {
            throw new \Exception('ElevenLabs TTS failed: ' . $response->body());
        }

        $audioData = $response->body();
        $audioFile = $this->saveAudioFile($audioData, 'elevenlabs');

        return [
            'success' => true,
            'provider' => 'elevenlabs',
            'audio_file' => $audioFile,
            'voice_id' => $voice,
            'text' => $text,
        ];
    }

    /**
     * Text-to-speech using OpenAI
     */
    private function ttsOpenAI(string $text, string $voice, array $options): array
    {
        $config = $this->providers['openai'];
        
        if (!$config['api_key']) {
            throw new \Exception('OpenAI API key not configured');
        }

        $availableVoices = ['alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer'];
        $selectedVoice = in_array($voice, $availableVoices) ? $voice : 'alloy';

        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $config['api_key'],
                'Content-Type' => 'application/json',
            ])
            ->post($config['base_url'] . '/audio/speech', [
                'model' => 'tts-1',
                'input' => $text,
                'voice' => $selectedVoice,
                'response_format' => 'mp3',
                'speed' => $options['speed'] ?? 1.0,
            ]);

        if (!$response->successful()) {
            throw new \Exception('OpenAI TTS failed: ' . $response->body());
        }

        $audioData = $response->body();
        $audioFile = $this->saveAudioFile($audioData, 'openai', 'mp3');

        return [
            'success' => true,
            'provider' => 'openai',
            'audio_file' => $audioFile,
            'voice' => $selectedVoice,
            'text' => $text,
        ];
    }

    /**
     * Text-to-speech using Azure
     */
    private function ttsAzure(string $text, string $voice, string $language, float $speed, float $pitch): array
    {
        $config = $this->providers['azure'];
        
        if (!$config['api_key'] || !$config['region']) {
            throw new \Exception('Azure Speech API credentials not configured');
        }

        $baseUrl = str_replace('{region}', $config['region'], $config['base_url']);
        
        // Get voice name based on language
        $voiceName = $this->getAzureVoiceName($language, $voice);
        
        $ssml = $this->buildSSML($text, $voiceName, $speed, $pitch);

        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'Ocp-Apim-Subscription-Key' => $config['api_key'],
                'Content-Type' => 'application/ssml+xml',
                'X-Microsoft-OutputFormat' => 'audio-16khz-128kbitrate-mono-mp3',
            ])
            ->post($baseUrl . '/cognitiveservices/v1', $ssml);

        if (!$response->successful()) {
            throw new \Exception('Azure TTS failed: ' . $response->body());
        }

        $audioData = $response->body();
        $audioFile = $this->saveAudioFile($audioData, 'azure', 'mp3');

        return [
            'success' => true,
            'provider' => 'azure',
            'audio_file' => $audioFile,
            'voice' => $voiceName,
            'text' => $text,
        ];
    }

    /**
     * Speech-to-text using OpenAI Whisper
     */
    private function sttOpenAI(string $audioFilePath, string $language): array
    {
        $config = $this->providers['openai'];
        
        if (!$config['api_key']) {
            throw new \Exception('OpenAI API key not configured');
        }

        $audioContent = Storage::get($audioFilePath);
        
        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $config['api_key'],
            ])
            ->attach('file', $audioContent, basename($audioFilePath))
            ->post($config['base_url'] . '/audio/transcriptions', [
                'model' => 'whisper-1',
                'language' => $language === 'ar' ? 'ar' : 'en',
                'response_format' => 'json',
            ]);

        if (!$response->successful()) {
            throw new \Exception('OpenAI STT failed: ' . $response->body());
        }

        $data = $response->json();

        return [
            'success' => true,
            'provider' => 'openai',
            'text' => $data['text'] ?? '',
            'language' => $language,
        ];
    }

    /**
     * Speech-to-text using Azure
     */
    private function sttAzure(string $audioFilePath, string $language): array
    {
        // Azure STT implementation would go here
        // For now, return a placeholder
        return [
            'success' => false,
            'error' => 'Azure STT not yet implemented',
            'text' => '',
        ];
    }

    /**
     * Save audio file to storage
     */
    private function saveAudioFile(string $audioData, string $provider, string $extension = 'mp3'): string
    {
        $filename = $provider . '_' . uniqid() . '.' . $extension;
        $path = 'audio/' . $filename;
        
        Storage::disk('public')->put($path, $audioData);
        
        return $path;
    }

    /**
     * Get default voice for provider and language
     */
    private function getDefaultVoice(string $provider, string $language): string
    {
        $defaultVoices = [
            'elevenlabs' => [
                'ar' => 'pNInz6obpgDQGcFmaJgB', // Adam (multilingual)
                'en' => 'pNInz6obpgDQGcFmaJgB',
            ],
            'openai' => [
                'ar' => 'alloy',
                'en' => 'alloy',
            ],
        ];

        return $defaultVoices[$provider][$language] ?? $defaultVoices[$provider]['en'];
    }

    /**
     * Get Azure voice name based on language
     */
    private function getAzureVoiceName(string $language, string $voice): string
    {
        $azureVoices = [
            'ar' => [
                'male' => 'ar-SA-HamedNeural',
                'female' => 'ar-SA-ZariyahNeural',
                'default' => 'ar-SA-ZariyahNeural',
            ],
            'en' => [
                'male' => 'en-US-DavisNeural',
                'female' => 'en-US-JennyNeural',
                'default' => 'en-US-JennyNeural',
            ],
        ];

        return $azureVoices[$language][$voice] ?? $azureVoices[$language]['default'];
    }

    /**
     * Build SSML for Azure TTS
     */
    private function buildSSML(string $text, string $voiceName, float $speed, float $pitch): string
    {
        $speedPercent = ($speed - 1) * 100;
        $pitchHz = $pitch > 1 ? '+' . (($pitch - 1) * 50) . 'Hz' : (($pitch - 1) * 50) . 'Hz';

        return '<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="ar-SA">' .
               '<voice name="' . $voiceName . '">' .
               '<prosody rate="' . $speedPercent . '%" pitch="' . $pitchHz . '">' .
               htmlspecialchars($text) .
               '</prosody>' .
               '</voice>' .
               '</speak>';
    }

    /**
     * Get available voices for each provider
     */
    public function getAvailableVoices(): array
    {
        return [
            'elevenlabs' => [
                'ar' => ['pNInz6obpgDQGcFmaJgB' => 'Adam (Multilingual)'],
                'en' => ['pNInz6obpgDQGcFmaJgB' => 'Adam (Multilingual)'],
            ],
            'openai' => [
                'voices' => ['alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer'],
            ],
            'azure' => [
                'ar' => [
                    'ar-SA-HamedNeural' => 'Hamed (Male)',
                    'ar-SA-ZariyahNeural' => 'Zariyah (Female)',
                ],
                'en' => [
                    'en-US-DavisNeural' => 'Davis (Male)',
                    'en-US-JennyNeural' => 'Jenny (Female)',
                ],
            ],
        ];
    }
}
