<?php

// Test Gemini Models List
function listGeminiModels() {
    $apiKey = 'AIzaSyAbPBjSTiK5s9LldpGotklEkXbaG0V65Rk';
    $baseUrl = 'https://generativelanguage.googleapis.com';

    echo "Listing available Gemini models...\n\n";

    $url = $baseUrl . '/v1beta/models?key=' . $apiKey;
    $response = file_get_contents($url);

    if ($response === false) {
        echo "Error: Failed to get models list\n";
        return;
    }

    $data = json_decode($response, true);

    if ($data === null) {
        echo "Error: Failed to parse JSON response\n";
        return;
    }

    echo "Available models:\n";
    foreach ($data['models'] as $model) {
        $name = $model['name'] ?? 'Unknown';
        $displayName = $model['displayName'] ?? 'Unknown';
        $supportedGenerationMethods = $model['supportedGenerationMethods'] ?? [];

        echo "- $name ($displayName)\n";
        echo "  Methods: " . implode(', ', $supportedGenerationMethods) . "\n";

        if (strpos($name, 'image') !== false || strpos($displayName, 'Image') !== false) {
            echo "  *** IMAGE MODEL FOUND ***\n";
        }
        echo "\n";
    }
}

// Test Gemini Image Generation
function testGeminiImageGeneration() {
    $apiKey = 'AIzaSyAbPBjSTiK5s9LldpGotklEkXbaG0V65Rk';
    $baseUrl = 'https://generativelanguage.googleapis.com';

    $prompt = "A beautiful mountain landscape with snow-capped peaks";

    echo "Testing Gemini Image Generation...\n";
    echo "Prompt: $prompt\n\n";

    $data = [
        'contents' => [
            [
                'parts' => [
                    [
                        'text' => $prompt
                    ]
                ]
            ]
        ],
        'generationConfig' => [
            'responseModalities' => ['TEXT', 'IMAGE'],
            'maxOutputTokens' => 8192,
            'temperature' => 0.8,
        ],
    ];

    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => json_encode($data),
            'timeout' => 120,
        ]
    ]);

    $url = $baseUrl . '/v1beta/models/gemini-2.0-flash-preview-image-generation:generateContent?key=' . $apiKey;
    $response = file_get_contents($url, false, $context);

    if ($response === false) {
        echo "Error: Failed to make request\n";
        return;
    }

    $data = json_decode($response, true);

    if ($data === null) {
        echo "Error: Failed to parse JSON response\n";
        echo "Raw response: " . $response . "\n";
        return;
    }

    echo "Success! Response received.\n";
    echo "Response structure:\n";
    print_r(array_keys($data));

    if (isset($data['candidates'][0]['content']['parts'])) {
        echo "\nParts found: " . count($data['candidates'][0]['content']['parts']) . "\n";

        foreach ($data['candidates'][0]['content']['parts'] as $index => $part) {
            echo "Part $index keys: " . implode(', ', array_keys($part)) . "\n";

            if (isset($part['inlineData'])) {
                echo "  - Has inline data with mime type: " . ($part['inlineData']['mimeType'] ?? 'unknown') . "\n";
                echo "  - Data size: " . strlen($part['inlineData']['data'] ?? '') . " characters\n";
            }

            if (isset($part['text'])) {
                echo "  - Text: " . substr($part['text'], 0, 100) . "...\n";
            }
        }
    } else {
        echo "Error in response:\n";
        print_r($data);
    }
}

listGeminiModels();
echo "\n" . str_repeat("=", 50) . "\n\n";
testGeminiImageGeneration();
