<?php

use App\Http\Controllers\ChatController;
use App\Http\Controllers\AdvancedFeaturesController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// WIDDX AI Chat Routes
Route::prefix('chat')->middleware('widdx.rate_limit')->group(function () {
    Route::post('/', [ChatController::class, 'chat']);
    Route::get('/personalities', [ChatController::class, 'getPersonalities']);
    Route::get('/history', [ChatController::class, 'getSessionHistory']);
    Route::put('/personality', [ChatController::class, 'updatePersonality']);
});

// Advanced Features Routes
Route::prefix('features')->middleware('widdx.rate_limit')->group(function () {
    // Live Search (Free & Paid)
    Route::post('/search', [AdvancedFeaturesController::class, 'search']);
    Route::post('/search-suggestions', [AdvancedFeaturesController::class, 'searchSuggestions']);
    Route::post('/deep-search', [AdvancedFeaturesController::class, 'deepSearch']);

    // Image Generation (Free & Paid)
    Route::post('/generate-image', [AdvancedFeaturesController::class, 'generateImage']);
    Route::post('/generate-placeholder', [AdvancedFeaturesController::class, 'generatePlaceholder']);

    // Voice Services (Free & Paid)
    Route::post('/text-to-speech', [AdvancedFeaturesController::class, 'textToSpeech']);
    Route::post('/speech-to-text', [AdvancedFeaturesController::class, 'speechToText']);
    Route::get('/speech-to-text-instructions', [AdvancedFeaturesController::class, 'speechToTextInstructions']);
    Route::get('/voice-widget', [AdvancedFeaturesController::class, 'voiceWidget']);

    // Think Mode
    Route::post('/think-mode', [AdvancedFeaturesController::class, 'thinkMode']);

    // Document Analysis
    Route::post('/analyze-document', [AdvancedFeaturesController::class, 'analyzeDocument']);

    // Vision/Image Analysis
    Route::post('/analyze-image', [AdvancedFeaturesController::class, 'analyzeImage']);

    // Capabilities
    Route::get('/capabilities', [AdvancedFeaturesController::class, 'getCapabilities']);
});

// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'service' => 'WIDDX AI',
        'timestamp' => now()->toISOString(),
        'features' => [
            'live_search' => true,
            'image_generation' => true,
            'voice_services' => true,
            'deep_search' => true,
            'think_mode' => true,
            'document_analysis' => true,
            'vision' => true,
        ],
    ]);
});
