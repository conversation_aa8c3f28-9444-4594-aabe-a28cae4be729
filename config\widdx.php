<?php

return [

    /*
    |--------------------------------------------------------------------------
    | WIDDX AI Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options specific to WIDDX AI.
    | These settings control the behavior and limits of the AI assistant.
    |
    */

    'default_personality' => env('WIDDX_DEFAULT_PERSONALITY', 'neutral'),

    'max_message_length' => env('WIDDX_MAX_MESSAGE_LENGTH', 10000),

    'session_cleanup_days' => env('WIDDX_SESSION_CLEANUP_DAYS', 30),

    'enable_logging' => env('WIDDX_ENABLE_LOGGING', true),

    /*
    |--------------------------------------------------------------------------
    | Multilingual Support Configuration
    |--------------------------------------------------------------------------
    |
    | These settings control the multilingual capabilities of WIDDX AI.
    | Language detection and response generation in multiple languages.
    |
    */

    'multilingual' => [
        'enabled' => env('WIDDX_MULTILINGUAL_ENABLED', true),

        'default_language' => env('WIDDX_DEFAULT_LANGUAGE', 'english'),

        'default_language_code' => env('WIDDX_DEFAULT_LANGUAGE_CODE', 'en'),

        'auto_detect_language' => env('WIDDX_AUTO_DETECT_LANGUAGE', true),

        'min_confidence_threshold' => env('WIDDX_LANGUAGE_MIN_CONFIDENCE', 0.3),

        'fallback_to_english' => env('WIDDX_FALLBACK_TO_ENGLISH', true),

        'supported_languages' => [
            'english', 'spanish', 'french', 'german', 'italian', 'portuguese',
            'russian', 'chinese', 'japanese', 'korean', 'arabic', 'hindi',
            'dutch', 'swedish', 'norwegian', 'danish', 'finnish', 'polish',
            'czech', 'hungarian', 'romanian', 'bulgarian', 'greek', 'turkish',
            'hebrew', 'thai', 'vietnamese', 'indonesian', 'malay', 'ukrainian',
            'croatian', 'serbian', 'slovenian', 'slovak', 'lithuanian', 'latvian', 'estonian'
        ],

        'language_override_enabled' => env('WIDDX_LANGUAGE_OVERRIDE_ENABLED', true),

        'store_language_history' => env('WIDDX_STORE_LANGUAGE_HISTORY', true),

        'update_preferred_language' => env('WIDDX_UPDATE_PREFERRED_LANGUAGE', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configure rate limiting for API requests to prevent abuse.
    |
    */

    'rate_limiting' => [
        'enabled' => env('WIDDX_RATE_LIMITING_ENABLED', true),
        'max_requests_per_minute' => env('WIDDX_MAX_REQUESTS_PER_MINUTE', 60),
        'max_requests_per_hour' => env('WIDDX_MAX_REQUESTS_PER_HOUR', 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Response Settings
    |--------------------------------------------------------------------------
    |
    | Configure default response behavior and limits.
    |
    */

    'response' => [
        'max_tokens' => env('WIDDX_MAX_TOKENS', 2000),
        'default_temperature' => env('WIDDX_DEFAULT_TEMPERATURE', 0.7),
        'timeout_seconds' => env('WIDDX_TIMEOUT_SECONDS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Configure security-related settings for WIDDX AI.
    |
    */

    'security' => [
        'content_filtering' => env('WIDDX_CONTENT_FILTERING', true),
        'log_user_messages' => env('WIDDX_LOG_USER_MESSAGES', true),
        'anonymize_logs' => env('WIDDX_ANONYMIZE_LOGS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Model Configuration
    |--------------------------------------------------------------------------
    |
    | Configure which AI models to use and their priorities.
    |
    */

    'models' => [
        'primary' => env('WIDDX_PRIMARY_MODEL', 'deepseek'),
        'fallback' => env('WIDDX_FALLBACK_MODEL', 'gemini'),
        'enable_model_comparison' => env('WIDDX_ENABLE_MODEL_COMPARISON', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Advanced Features Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for advanced AI capabilities like search, image generation,
    | voice services, document analysis, and vision.
    |
    */

    'features' => [
        'live_search' => [
            'enabled' => env('WIDDX_LIVE_SEARCH_ENABLED', true),
            'default_provider' => env('WIDDX_SEARCH_DEFAULT_PROVIDER', 'duckduckgo'),
            'cache_minutes' => env('WIDDX_SEARCH_CACHE_MINUTES', 15),
            'max_results' => env('WIDDX_SEARCH_MAX_RESULTS', 10),
            'use_free_alternatives' => env('WIDDX_USE_FREE_SEARCH', true),
        ],

        'image_generation' => [
            'enabled' => env('WIDDX_IMAGE_GEN_ENABLED', true),
            'default_provider' => env('WIDDX_IMAGE_DEFAULT_PROVIDER', 'gemini'),
            'default_size' => env('WIDDX_IMAGE_DEFAULT_SIZE', '1024x1024'),
            'default_quality' => env('WIDDX_IMAGE_DEFAULT_QUALITY', 'standard'),
            'use_free_alternatives' => env('WIDDX_USE_FREE_IMAGE', false), // Use real image generation with Gemini
        ],

        'voice_services' => [
            'enabled' => env('WIDDX_VOICE_ENABLED', true),
            'tts_provider' => env('WIDDX_TTS_PROVIDER', 'browser'),
            'stt_provider' => env('WIDDX_STT_PROVIDER', 'browser'),
            'default_voice' => env('WIDDX_DEFAULT_VOICE', 'default'),
            'use_free_alternatives' => env('WIDDX_USE_FREE_VOICE', true),
        ],

        'deep_search' => [
            'enabled' => env('WIDDX_DEEP_SEARCH_ENABLED', true),
            'max_sources' => env('WIDDX_DEEP_SEARCH_MAX_SOURCES', 5),
            'cache_minutes' => env('WIDDX_DEEP_SEARCH_CACHE_MINUTES', 30),
            'use_free_search' => env('WIDDX_USE_FREE_DEEP_SEARCH', true),
        ],

        'think_mode' => [
            'enabled' => env('WIDDX_THINK_MODE_ENABLED', true),
            'max_steps' => env('WIDDX_THINK_MAX_STEPS', 7),
            'show_reasoning' => env('WIDDX_SHOW_REASONING', true),
        ],

        'document_analysis' => [
            'enabled' => env('WIDDX_DOCUMENT_ANALYSIS_ENABLED', true),
            'max_file_size' => env('WIDDX_MAX_DOCUMENT_SIZE', 10485760), // 10MB
            'supported_types' => ['pdf', 'docx', 'doc', 'txt', 'rtf', 'xlsx', 'xls', 'csv'],
            'use_local_processing' => env('WIDDX_USE_LOCAL_DOCUMENT_PROCESSING', true),
        ],

        'vision' => [
            'enabled' => env('WIDDX_VISION_ENABLED', true),
            'max_image_size' => env('WIDDX_MAX_IMAGE_SIZE', 20971520), // 20MB
            'supported_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'],
            'use_gemini_vision' => env('WIDDX_USE_GEMINI_VISION', true),
        ],

        // Free alternatives configuration
        'free_alternatives' => [
            'enabled' => env('WIDDX_USE_FREE_ALTERNATIVES', true),
            'search_provider' => env('WIDDX_FREE_SEARCH_PROVIDER', 'duckduckgo'),
            'image_provider' => env('WIDDX_FREE_IMAGE_PROVIDER', 'description'),
            'voice_provider' => env('WIDDX_FREE_VOICE_PROVIDER', 'browser'),
            'fallback_to_paid' => env('WIDDX_FALLBACK_TO_PAID', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Personality System
    |--------------------------------------------------------------------------
    |
    | Configure the personality system behavior.
    |
    */

    'personalities' => [
        'allow_custom' => env('WIDDX_ALLOW_CUSTOM_PERSONALITIES', false),
        'cache_duration' => env('WIDDX_PERSONALITY_CACHE_DURATION', 3600), // seconds
    ],

];
