[2025-07-11 15:02:11] local.INFO: Language detection {"message":"مرحبا","detected_code":"en","result":{"ar":0.10665806315835791,"fa":0.10429469856083184,"ur":0.09534008946245603,"so":0.0009493891797556719,"he":0.00024660361013562564,"et":8.336394469270356e-5,"tr":7.585657312061947e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.5951028945866016e-5,"id":4.409251662944479e-5,"ko":4.0264005391582206e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"lv":3.423586686356094e-5,"sl":3.357427000042656e-5,"lt":1.7519586205815487e-5,"vi":1.7137537158470558e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:02:11] local.INFO: Demo response generation {"user_message":"مرحبا","target_language":"english","target_language_code":"en","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:02:22] local.INFO: Language detection {"message":"هل تتحدث العربية","detected_code":"en","result":{"ar":0.2787899103598457,"fa":0.2032450883278337,"ur":0.17389465317822286,"so":0.0021535776614310647,"he":0.0004893596969853614,"et":0.0001813648743269959,"tr":0.0001607733849069996,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":9.178621496161641e-5,"ko":8.284688020362204e-5,"lv":6.81674149105569e-5,"sl":6.577254532870448e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"vi":3.22086871366275e-5,"lt":2.4691346640944635e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:02:22] local.INFO: Demo response generation {"user_message":"هل تتحدث العربية","target_language":"english","target_language_code":"en","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:03:57] local.INFO: Language detection {"message":"عرفني بنفسك","detected_code":"fa","max_score":0.1365240767108165,"result":{"fa":0.1365240767108165,"ar":0.1294126201142054,"ur":0.1190235484879895,"so":0.0010506108202443282,"he":0.00017653342135259546,"et":8.87878020568456e-5,"tr":7.68469635820589e-5,"ro":4.8190658928101506e-5,"ko":3.6891104416371135e-5,"lv":2.8605968757108697e-5,"sl":2.6969495574113137e-5,"lt":1.5368058075276744e-5,"vi":1.3274291560492922e-5,"af":0,"bg":0,"bn":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"id":0,"it":0,"ja":0,"kn":0,"mk":0,"ml":0,"mr":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"sw":0,"ta":0,"te":0,"th":0,"tl":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:03:57] local.INFO: Demo response generation {"user_message":"عرفني بنفسك","target_language":"english","target_language_code":"fa","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:04:05] local.INFO: Language detection {"message":"من انت","detected_code":"ar","max_score":0.13230813214304302,"result":{"ar":0.13230813214304302,"fa":0.12348434442443165,"ur":0.11046516702006076,"so":0.000893542757417103,"he":0.0002521712026552075,"tr":0.00011125386183502846,"et":8.596442425085385e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.784313013775462e-5,"ko":4.771249504517333e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"sl":3.759217444310056e-5,"tl":3.5574798256092454e-5,"lv":3.317075100558349e-5,"vi":2.0169735738278586e-5,"lt":1.4702108892014752e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:04:05] local.INFO: Demo response generation {"user_message":"من انت","target_language":"arabic","target_language_code":"ar","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:04:15] testing.INFO: Language detection {"message":"مرحبا كيف حالك اليوم؟","detected_code":"ar","max_score":0.28046189253817905,"result":{"ar":0.28046189253817905,"ur":0.19293490560044554,"fa":0.18912464904565024,"so":0.0021221640488656196,"he":0.0005383816701455821,"et":0.00017594101696285385,"tr":0.0001607733849069996,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":9.750113284732075e-5,"ko":9.268450804798767e-5,"sl":7.303779719764926e-5,"lv":7.227571893418421e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"vi":3.640884368791714e-5,"tl":3.5574798256092454e-5,"lt":2.8021092557254597e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:04:26] testing.INFO: Language detection {"message":"مرحبا WIDDX، كيف حالك؟","detected_code":"ar","max_score":0.2254548385880025,"result":{"ar":0.2254548385880025,"ur":0.16141399310693785,"fa":0.16098778822806994,"i-klingon":0.05252088566921774,"so":0.0055671902268760905,"de":0.004569887288293264,"af":0.003582728427261314,"sw":0.0035257521909784085,"en":0.003359879079412263,"nl":0.003259341650457576,"pl":0.0031474251977982592,"id":0.0030264098320449033,"tl":0.002239201540667176,"it":0.0021540103774840905,"no":0.002112832595593519,"sv":0.002063396977528355,"da":0.0020333081296588546,"hr":0.0018921552852247205,"fr":0.001882076530513198,"ro":0.0016714590243283667,"es":0.0016475645869402065,"pt":0.001614819274420593,"tr":0.001513756798617855,"sq":0.0014167696829588022,"vi":0.001312178320229369,"fi":0.0012602586854005803,"hu":0.001202439071440162,"et":0.0011571391039609316,"sl":0.0011020616528172804,"cs":0.001087953745512772,"sk":0.0010638736791892375,"lv":0.001021446107800376,"lt":0.0009894980326068186,"he":0.0008749267952113635,"uk":0.000768438320795314,"ru":0.0007260804256376656,"bg":0.0005529692872505566,"mk":0.0004420384518704154,"th":0.0004299494367175124,"el":0.00036735737753802473,"pa":0.00032783205547745343,"te":0.0003263609232347559,"bn":0.00031039954615504,"ml":0.00022101883710422965,"ta":0.00021992670187400177,"kn":0.0001231275479622391,"ko":7.188495203418604e-5,"mr":4.015296481701335e-5,"gu":0,"hi":0,"ja":0,"ne":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:04:26] testing.INFO: Demo response generation {"user_message":"مرحبا WIDDX، كيف حالك؟","target_language":"arabic","target_language_code":"ar","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:04:30] local.INFO: Language detection {"message":"ماهي المميزات التي لديك","detected_code":"ar","max_score":0.2700098578786724,"result":{"ar":0.2700098578786724,"fa":0.20277634503752118,"ur":0.178449164529287,"so":0.00187434554973822,"he":0.00045201608862231236,"tr":0.0001607733849069996,"et":0.0001515708085321883,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":8.174649435159526e-5,"ko":7.841994767365749e-5,"sl":6.037864621388186e-5,"lv":6.0255125679867255e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"vi":3.24108337086147e-5,"lt":2.146405444513652e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:04:30] local.INFO: Demo response generation {"user_message":"ماهي المميزات التي لديك","target_language":"arabic","target_language_code":"ar","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:04:56] local.INFO: Language detection {"message":"مرحبا WIDDX، كيف حالك اليوم؟","detected_code":"ar","max_score":0.28549878788256056,"result":{"ar":0.28549878788256056,"ur":0.19636479362725628,"fa":0.19254432796494003,"i-klingon":0.05252088566921774,"so":0.005982547993019198,"de":0.004569887288293264,"af":0.003582728427261314,"sw":0.0035257521909784085,"en":0.003359879079412263,"nl":0.003259341650457576,"pl":0.0031474251977982592,"id":0.0030264098320449033,"tl":0.002239201540667176,"it":0.0021540103774840905,"no":0.002112832595593519,"sv":0.002063396977528355,"da":0.0020333081296588546,"hr":0.0018921552852247205,"fr":0.001882076530513198,"ro":0.0016919246086487946,"es":0.0016475645869402065,"pt":0.001614819274420593,"tr":0.001513756798617855,"sq":0.0014167696829588022,"vi":0.0013160191050971257,"fi":0.0012602586854005803,"hu":0.001202439071440162,"et":0.0011900537452255195,"sl":0.001117252633997801,"cs":0.001087953745512772,"sk":0.0010638736791892375,"lv":0.0010362055704037777,"lt":0.0009894980326068186,"he":0.0009750529306041689,"uk":0.000768438320795314,"ru":0.0007260804256376656,"bg":0.0005529692872505566,"mk":0.0004420384518704154,"th":0.0004299494367175124,"el":0.00036735737753802473,"pa":0.00032783205547745343,"te":0.0003263609232347559,"bn":0.00031039954615504,"ml":0.00022101883710422965,"ta":0.00021992670187400177,"kn":0.0001231275479622391,"ko":9.268450804798767e-5,"mr":4.015296481701335e-5,"gu":0,"hi":0,"ja":0,"ne":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:04:56] local.INFO: Demo response generation {"user_message":"مرحبا WIDDX، كيف حالك اليوم؟","target_language":"arabic","target_language_code":"ar","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:04:57] local.INFO: Language detection {"message":"من أنت؟","detected_code":"en","max_score":0.07518300291489749,"result":{"ar":0.07518300291489749,"fa":0.06011236650787777,"ur":0.04987940896081842,"so":0.0004223385689354276,"he":8.686349630144376e-5,"et":3.63324143981569e-5,"tr":3.539728871440899e-5,"ro":2.1932928101892352e-5,"ko":2.16427812576044e-5,"sl":1.722745329530084e-5,"lv":1.2172752662599446e-5,"vi":7.389580242643269e-6,"lt":7.069306714627302e-6,"af":0,"bg":0,"bn":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"id":0,"it":0,"ja":0,"kn":0,"mk":0,"ml":0,"mr":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"sw":0,"ta":0,"te":0,"th":0,"tl":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:04:57] local.INFO: Demo response generation {"user_message":"من أنت؟","target_language":"english","target_language_code":"en","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:04:59] local.INFO: Demo response generation {"user_message":"Hello, how are you?","target_language":"arabic","target_language_code":"ar","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:05:00] local.INFO: Language detection {"message":"Hello WIDDX, how are you?","detected_code":"i-klingon","max_score":0.288660396097447,"result":{"i-klingon":0.288660396097447,"so":0.2251762652705061,"it":0.2051781527757865,"es":0.20514165977659507,"pt":0.19773195234502391,"en":0.19449762037595475,"fr":0.19407047116903203,"sw":0.18906809016148896,"nl":0.18797020397041977,"af":0.18760371688835334,"ro":0.182986534997018,"pl":0.17964601048127246,"da":0.17925948662443644,"no":0.17904169105474282,"id":0.17765386471057826,"tr":0.17491810479614922,"de":0.17178216554616768,"sk":0.1704956842963735,"tl":0.16999200804423525,"sv":0.1685341498833703,"hr":0.16805364402459327,"fi":0.16638470236160238,"sl":0.1626934820508375,"et":0.16226509496654148,"hu":0.15955428431802338,"cs":0.15888924746796712,"sq":0.15709617302255402,"lt":0.14531077773694998,"lv":0.13323610347144083,"vi":0.10749503337102992,"th":0.011386245325398846,"pa":0.010812550946873487,"te":0.01005511106980675,"he":0.009714453117113992,"bn":0.00858490536157148,"el":0.008240332306528167,"ml":0.008234716574566434,"bg":0.008224936854283856,"mk":0.007126769105963544,"ta":0.006506548258495057,"ru":0.005993819095510787,"uk":0.004012350093851895,"kn":0.003549050091263221,"gu":0.0007108145489020445,"ar":0,"fa":0,"hi":0,"ja":0,"ko":0,"mr":0,"ne":0,"ur":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:05:00] local.INFO: Demo response generation {"user_message":"Hello WIDDX, how are you?","target_language":"english","target_language_code":"i-klingon","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:05:01] local.INFO: Demo response generation {"user_message":"مرحبا كيف حالك؟","target_language":"english","target_language_code":"en","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:06:05] local.INFO: Language detection {"message":"من أنت؟","detected_code":"ar","max_score":0.07518300291489749,"result":{"ar":0.07518300291489749,"fa":0.06011236650787777,"ur":0.04987940896081842,"so":0.0004223385689354276,"he":8.686349630144376e-5,"et":3.63324143981569e-5,"tr":3.539728871440899e-5,"ro":2.1932928101892352e-5,"ko":2.16427812576044e-5,"sl":1.722745329530084e-5,"lv":1.2172752662599446e-5,"vi":7.389580242643269e-6,"lt":7.069306714627302e-6,"af":0,"bg":0,"bn":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"id":0,"it":0,"ja":0,"kn":0,"mk":0,"ml":0,"mr":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"sw":0,"ta":0,"te":0,"th":0,"tl":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:06:05] local.INFO: Demo response generation {"user_message":"من أنت؟","target_language":"arabic","target_language_code":"ar","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:06:06] local.INFO: Language detection {"message":"كيف حالك؟","detected_code":"ar","max_score":0.1598293817540191,"result":{"ar":0.1598293817540191,"ur":0.10098877954318089,"fa":0.09559496301600161,"so":0.0011902268760907505,"he":0.0003028679800692862,"tr":0.0001607733849069996,"te":0.00010273653089592427,"et":0.00010089860685623122,"sw":0.00010019152661705413,"bn":5.912372307715048e-5,"ro":5.255407596245685e-5,"ml":5.050307580022938e-5,"ko":5.0312439546898535e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"lv":3.864848970375324e-5,"mk":3.856232051858228e-5,"sl":3.847281103327568e-5,"tl":3.5574798256092454e-5,"vi":2.4369892289568225e-5,"lt":1.8134308528826557e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:06:07] local.INFO: Demo response generation {"user_message":"كيف حالك؟","target_language":"arabic","target_language_code":"ar","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:06:08] local.INFO: Language detection {"message":"شكرا","detected_code":"en","max_score":0.08809691993279176,"result":{"fa":0.08809691993279176,"ar":0.07559430450478706,"ur":0.06842706784253162,"so":0.0005863874345549738,"he":0.00015412725633476602,"tr":7.585657312061947e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"et":5.0746501091904215e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"ro":2.6566645306517497e-5,"ko":2.5718369935984447e-5,"lv":2.2976070650656452e-5,"sl":2.102519859043106e-5,"vi":1.293738060718092e-5,"lt":1.0962548093697411e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:06:08] local.INFO: Demo response generation {"user_message":"شكرا","target_language":"english","target_language_code":"en","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:06:09] local.INFO: Language detection {"message":"مرحبا","detected_code":"ar","max_score":0.10665806315835791,"result":{"ar":0.10665806315835791,"fa":0.10429469856083184,"ur":0.09534008946245603,"so":0.0009493891797556719,"he":0.00024660361013562564,"et":8.336394469270356e-5,"tr":7.585657312061947e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.5951028945866016e-5,"id":4.409251662944479e-5,"ko":4.0264005391582206e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"lv":3.423586686356094e-5,"sl":3.357427000042656e-5,"lt":1.7519586205815487e-5,"vi":1.7137537158470558e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:06:09] local.INFO: Demo response generation {"user_message":"مرحبا","target_language":"arabic","target_language_code":"ar","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:06:11] local.INFO: Language detection {"message":"أهلا","detected_code":"ar","max_score":0.09861408987357578,"result":{"ar":0.09861408987357578,"fa":0.07371955239318016,"ur":0.05696203944685825,"so":0.0006945898778359512,"he":0.00019681213231822694,"tr":0.00011932371003934969,"te":0.00010273653089592427,"sw":0.00010019152661705413,"et":6.018252691719241e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"ko":3.176148418323762e-5,"ro":3.038946200033324e-5,"lv":2.571493999974133e-5,"sl":2.4602784738017493e-5,"vi":1.4869003406169738e-5,"lt":1.3062849363985233e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:06:11] local.INFO: Demo response generation {"user_message":"أهلا","target_language":"arabic","target_language_code":"ar","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:08:18] local.INFO: Language detection {"message":"مرحبا","detected_code":"ar","max_score":0.10665806315835791,"result":{"ar":0.10665806315835791,"fa":0.10429469856083184,"ur":0.09534008946245603,"so":0.0009493891797556719,"he":0.00024660361013562564,"et":8.336394469270356e-5,"tr":7.585657312061947e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.5951028945866016e-5,"id":4.409251662944479e-5,"ko":4.0264005391582206e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"lv":3.423586686356094e-5,"sl":3.357427000042656e-5,"lt":1.7519586205815487e-5,"vi":1.7137537158470558e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:08:18] local.INFO: Demo response generation {"user_message":"مرحبا","target_language":"arabic","target_language_code":"ar","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:08:25] local.INFO: Language detection {"message":"كيف حالك","detected_code":"ar","max_score":0.1598293817540191,"result":{"ar":0.1598293817540191,"ur":0.10098877954318089,"fa":0.09559496301600161,"so":0.0011902268760907505,"he":0.0003028679800692862,"tr":0.0001607733849069996,"te":0.00010273653089592427,"et":0.00010089860685623122,"sw":0.00010019152661705413,"bn":5.912372307715048e-5,"ro":5.255407596245685e-5,"ml":5.050307580022938e-5,"ko":5.0312439546898535e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"lv":3.864848970375324e-5,"mk":3.856232051858228e-5,"sl":3.847281103327568e-5,"tl":3.5574798256092454e-5,"vi":2.4369892289568225e-5,"lt":1.8134308528826557e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:08:25] local.INFO: Demo response generation {"user_message":"كيف حالك","target_language":"arabic","target_language_code":"ar","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:08:31] local.INFO: Language detection {"message":"من انت","detected_code":"ar","max_score":0.13230813214304302,"result":{"ar":0.13230813214304302,"fa":0.12348434442443165,"ur":0.11046516702006076,"so":0.000893542757417103,"he":0.0002521712026552075,"tr":0.00011125386183502846,"et":8.596442425085385e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.784313013775462e-5,"ko":4.771249504517333e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"sl":3.759217444310056e-5,"tl":3.5574798256092454e-5,"lv":3.317075100558349e-5,"vi":2.0169735738278586e-5,"lt":1.4702108892014752e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:08:31] local.INFO: Demo response generation {"user_message":"من انت","target_language":"arabic","target_language_code":"ar","personality":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative."} 
[2025-07-11 15:11:19] local.INFO: Language detection {"message":"الان كيف تشعر","detected_code":"ar","max_score":0.23024159889499438,"result":{"ar":0.23024159889499438,"fa":0.19561351492491824,"ur":0.16962408841272641,"so":0.001612565445026178,"he":0.0003836207040931401,"tr":0.00019617067362140859,"et":0.00014384366927368457,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":7.452561837438774e-5,"ko":7.04093078575312e-5,"bn":5.912372307715048e-5,"sl":5.553514496791869e-5,"lv":5.1277720591200164e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"vi":3.155732596022429e-5,"lt":2.520361524345386e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:11:44] local.INFO: Language detection {"message":"من انت","detected_code":"ar","max_score":0.13230813214304302,"result":{"ar":0.13230813214304302,"fa":0.12348434442443165,"ur":0.11046516702006076,"so":0.000893542757417103,"he":0.0002521712026552075,"tr":0.00011125386183502846,"et":8.596442425085385e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.784313013775462e-5,"ko":4.771249504517333e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"sl":3.759217444310056e-5,"tl":3.5574798256092454e-5,"lv":3.317075100558349e-5,"vi":2.0169735738278586e-5,"lt":1.4702108892014752e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:12:01] local.ERROR: Gemini API Error {"status":503,"body":"{
  \"error\": {
    \"code\": 503,
    \"message\": \"The model is overloaded. Please try again later.\",
    \"status\": \"UNAVAILABLE\"
  }
}
"} 
[2025-07-11 15:12:01] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 503,
    \"message\": \"The model is overloaded. Please try again later.\",
    \"status\": \"UNAVAILABLE\"
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(165): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(29): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x85\\xD9\\x86 \\xD8\\xA7\\xD9\\x86\\xD8\\xAA', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(85): App\\Services\\ModelMergerService->processMessage('\\xD9\\x85\\xD9\\x86 \\xD8\\xA7\\xD9\\x86\\xD8\\xAA', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(Illuminate\\Http\\Request))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
[2025-07-11 15:12:27] local.INFO: Language detection {"message":"هل تستطيع انشاء الصور","detected_code":"ar","max_score":0.28844944331736816,"result":{"ar":0.28844944331736816,"fa":0.25306126601057083,"ur":0.21762770441870816,"so":0.0020488656195462476,"he":0.00048374683948594556,"tr":0.00019617067362140859,"et":0.00017787280177747978,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":9.441198804423733e-5,"ko":9.057644493848075e-5,"lv":6.984116840166432e-5,"sl":6.692838085330933e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"vi":3.5398110827981126e-5,"lt":2.520361524345386e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:12:51] local.ERROR: Gemini API Error {"status":503,"body":"{
  \"error\": {
    \"code\": 503,
    \"message\": \"The model is overloaded. Please try again later.\",
    \"status\": \"UNAVAILABLE\"
  }
}
"} 
[2025-07-11 15:12:51] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 503,
    \"message\": \"The model is overloaded. Please try again later.\",
    \"status\": \"UNAVAILABLE\"
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(165): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(29): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x87\\xD9\\x84 \\xD8\\xAA\\xD8\\xB3\\xD8\\xAA\\xD8\\xB7\\xD9\\x8A...', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(85): App\\Services\\ModelMergerService->processMessage('\\xD9\\x87\\xD9\\x84 \\xD8\\xAA\\xD8\\xB3\\xD8\\xAA\\xD8\\xB7\\xD9\\x8A...', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(Illuminate\\Http\\Request))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
[2025-07-11 15:12:51] local.INFO: Language detection {"message":"مرحبا WIDDX، كيف حالك اليوم؟","detected_code":"ar","max_score":0.28549878788256056,"result":{"ar":0.28549878788256056,"ur":0.19636479362725628,"fa":0.19254432796494003,"i-klingon":0.05252088566921774,"so":0.005982547993019198,"de":0.004569887288293264,"af":0.003582728427261314,"sw":0.0035257521909784085,"en":0.003359879079412263,"nl":0.003259341650457576,"pl":0.0031474251977982592,"id":0.0030264098320449033,"tl":0.002239201540667176,"it":0.0021540103774840905,"no":0.002112832595593519,"sv":0.002063396977528355,"da":0.0020333081296588546,"hr":0.0018921552852247205,"fr":0.001882076530513198,"ro":0.0016919246086487946,"es":0.0016475645869402065,"pt":0.001614819274420593,"tr":0.001513756798617855,"sq":0.0014167696829588022,"vi":0.0013160191050971257,"fi":0.0012602586854005803,"hu":0.001202439071440162,"et":0.0011900537452255195,"sl":0.001117252633997801,"cs":0.001087953745512772,"sk":0.0010638736791892375,"lv":0.0010362055704037777,"lt":0.0009894980326068186,"he":0.0009750529306041689,"uk":0.000768438320795314,"ru":0.0007260804256376656,"bg":0.0005529692872505566,"mk":0.0004420384518704154,"th":0.0004299494367175124,"el":0.00036735737753802473,"pa":0.00032783205547745343,"te":0.0003263609232347559,"bn":0.00031039954615504,"ml":0.00022101883710422965,"ta":0.00021992670187400177,"kn":0.0001231275479622391,"ko":9.268450804798767e-5,"mr":4.015296481701335e-5,"gu":0,"hi":0,"ja":0,"ne":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:13:16] local.INFO: Language detection {"message":"ماهي القدرات التي تتمتع بها حاليا","detected_code":"ar","max_score":0.3297174039723775,"result":{"ar":0.3297174039723775,"fa":0.27246441563738555,"ur":0.2370243615616291,"so":0.0024537521815008725,"he":0.0005383816701455821,"et":0.00019956823123404793,"tr":0.0001607733849069996,"ro":0.00010452893727433556,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ko":9.141967018228352e-5,"sl":7.276259826321953e-5,"lv":7.197140011761922e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"vi":3.640884368791714e-5,"tl":3.5574798256092454e-5,"lt":2.8021092557254597e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:14:33] local.INFO: Language detection {"message":"Hello WIDDX","detected_code":"i-klingon","max_score":0.15177893322427996,"result":{"i-klingon":0.15177893322427996,"it":0.11042812541831919,"es":0.1030260359300196,"nl":0.09825548875637126,"pt":0.0959349709920655,"no":0.09321388345923623,"da":0.09299251288178571,"af":0.09204704832130148,"fr":0.08812889047840193,"sl":0.08482869554329085,"de":0.08273141220132521,"en":0.0825913028205173,"sk":0.0812603981432799,"fi":0.07828879578046,"cs":0.07743264289921348,"hr":0.07696800032077268,"ro":0.07663905679141728,"hu":0.07610076381421188,"sv":0.07553507657018935,"et":0.07529205614420506,"pl":0.07122470513585975,"tr":0.06813545240210953,"so":0.06460034904013962,"sq":0.060668068435185636,"lt":0.06008587978213626,"id":0.05394596764213532,"tl":0.0526678701260013,"lv":0.05027346849653571,"sw":0.047164480842143176,"vi":0.03651937294133369,"th":0.005217651971872684,"he":0.004661342358325366,"pa":0.004589648776684348,"te":0.0040045465452754085,"bg":0.003952488560656783,"el":0.003919035295826109,"bn":0.003682915250014165,"mk":0.003405862234443674,"ml":0.0033761034650540435,"ru":0.0031284590846584297,"ta":0.002781345491421802,"uk":0.002201782786692418,"kn":0.0014626470257931917,"gu":0.00029806854369373784,"ar":0,"fa":0,"hi":0,"ja":0,"ko":0,"mr":0,"ne":0,"ur":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:17:00] local.INFO: Language detection {"message":"hi","detected_code":"en","max_score":0.05160012052669572,"result":{"sw":0.05160012052669572,"sq":0.043470945670888154,"lt":0.04207354240756715,"de":0.04198868851629741,"fi":0.040856192399019935,"en":0.03997461911921514,"vi":0.0394393577129586,"it":0.03820003126534227,"et":0.03760605498632296,"hr":0.03617441077546277,"so":0.0353717277486911,"id":0.034857228341413325,"ro":0.0347732287760789,"af":0.03403485122591946,"pl":0.03267997440173418,"tr":0.032562791213769364,"sl":0.03222920768850783,"nl":0.031946678705619375,"lv":0.031388812175187214,"tl":0.030650318139233884,"fr":0.027509290282850263,"sv":0.026858840179172498,"pt":0.026565034063112017,"sk":0.026482493476614827,"da":0.02546515844521613,"no":0.025463885405603418,"es":0.025193121611995935,"i-klingon":0.025062803061284104,"cs":0.023833516933200943,"hu":0.02038579924121644,"pa":0.002415915507932945,"th":0.0022678535206016115,"te":0.002267464211929127,"ml":0.0018509648802696973,"he":0.001817570000254389,"bn":0.001759458651929844,"el":0.0016172881613752482,"bg":0.001616850208409635,"ta":0.0013873160351678833,"mk":0.0013738421782283803,"ru":0.0010162897971751875,"kn":0.0008158891364970348,"uk":0.0006829697735210298,"gu":0.0001753435410202518,"ar":0,"fa":0,"hi":0,"ja":0,"ko":0,"mr":0,"ne":0,"ur":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:17:13] local.INFO: Language detection {"message":"مرحبا","detected_code":"ar","max_score":0.10665806315835791,"result":{"ar":0.10665806315835791,"fa":0.10429469856083184,"ur":0.09534008946245603,"so":0.0009493891797556719,"he":0.00024660361013562564,"et":8.336394469270356e-5,"tr":7.585657312061947e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.5951028945866016e-5,"id":4.409251662944479e-5,"ko":4.0264005391582206e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"lv":3.423586686356094e-5,"sl":3.357427000042656e-5,"lt":1.7519586205815487e-5,"vi":1.7137537158470558e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:37:24] local.INFO: Language detection {"message":"ماهو النيترون وماهي القوه التي يكونها وكيف يوجد","detected_code":"ar","max_score":0.36432119874260527,"result":{"ar":0.36432119874260527,"fa":0.31406118093072843,"ur":0.25356206830833394,"so":0.002387434554973822,"he":0.0005327688126461663,"et":0.00019741354817158054,"tr":0.00019617067362140859,"ro":0.00010993494067973156,"ko":0.00010568423055661369,"te":0.00010273653089592427,"sw":0.00010019152661705413,"sl":8.16240039518567e-5,"lv":7.851425467376642e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"vi":3.9598267379270765e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"lt":2.853336115976382e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:37:54] local.ERROR: DeepSeek Client Error {"message":"cURL error 28: Operation timed out after 30015 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.deepseek.com/v1/chat/completions","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(947): Illuminate\\Http\\Client\\PendingRequest->marshalConnectionException(Object(GuzzleHttp\\Exception\\ConnectException))
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(1)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(910): retry(0, Object(Closure), 100, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(809): Illuminate\\Http\\Client\\PendingRequest->send('POST', 'https://api.dee...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\DeepSeekClient.php(37): Illuminate\\Http\\Client\\PendingRequest->post('https://api.dee...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(199): App\\Services\\DeepSeekClient->chat(Array, Array)
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(72): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x85\\xD8\\xA7\\xD9\\x87\\xD9\\x88 \\xD8\\xA7\\xD9\\x84\\xD9\\x86...', Array, Array)
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(96): App\\Services\\ModelMergerService->processMessage('\\xD9\\x85\\xD8\\xA7\\xD9\\x87\\xD9\\x88 \\xD8\\xA7\\xD9\\x84\\xD9\\x86...', Array, Array)
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#48 {main}"} 
[2025-07-11 15:38:34] local.INFO: Language detection {"message":"قم بإنشاء صورة لرجل يركب فوق جمل في فلسطين","detected_code":"ar","max_score":0.33330596060140083,"result":{"ar":0.33330596060140083,"fa":0.25688270191706275,"ur":0.24733614871413195,"so":0.0021291448516579407,"he":0.00042236752683104314,"tr":0.00019617067362140859,"et":0.00017348913623659788,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":8.993272807976635e-5,"ko":8.439279315059377e-5,"lv":7.379731301700913e-5,"sl":6.27453570499775e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"vi":3.23434515179523e-5,"lt":3.1760653355571936e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:42:01] local.INFO: Language detection {"message":"ابحث لي في الانترنت عن منصات نماذج ذكاء صناعي مجانية صينية","detected_code":"ar","max_score":0.4131934635053123,"result":{"ar":0.4131934635053123,"fa":0.2697638141759744,"ur":0.26686513836154907,"so":0.0026352530541012215,"he":0.0005762231932868051,"et":0.00022289824784145338,"tr":0.00019617067362140859,"ro":0.00011232902790212122,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ko":9.858708475460704e-5,"sl":7.909217375510322e-5,"lv":7.836209526548393e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"vi":3.9598267379270765e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"lt":3.1760653355571936e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:42:31] local.ERROR: DeepSeek Client Error {"message":"cURL error 28: Operation timed out after 30001 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.deepseek.com/v1/chat/completions","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(947): Illuminate\\Http\\Client\\PendingRequest->marshalConnectionException(Object(GuzzleHttp\\Exception\\ConnectException))
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(1)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(910): retry(0, Object(Closure), 100, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(809): Illuminate\\Http\\Client\\PendingRequest->send('POST', 'https://api.dee...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\DeepSeekClient.php(37): Illuminate\\Http\\Client\\PendingRequest->post('https://api.dee...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(199): App\\Services\\DeepSeekClient->chat(Array, Array)
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(72): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD8\\xA7\\xD8\\xA8\\xD8\\xAD\\xD8\\xAB \\xD9\\x84\\xD9\\x8A \\xD9...', Array, Array)
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(96): App\\Services\\ModelMergerService->processMessage('\\xD8\\xA7\\xD8\\xA8\\xD8\\xAD\\xD8\\xAB \\xD9\\x84\\xD9\\x8A \\xD9...', Array, Array)
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#48 {main}"} 
[2025-07-11 15:48:08] local.INFO: Language detection {"message":"ابحث عن روابط لمنصات ذكاء صناعي","detected_code":"ar","max_score":0.2883248021652144,"result":{"ar":0.2883248021652144,"fa":0.22678932638798757,"ur":0.22308784097692239,"so":0.001912739965095986,"he":0.00042236752683104314,"tr":0.00019617067362140859,"et":0.0001694769677754517,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":9.306048719288832e-5,"ko":8.502521208344585e-5,"sl":6.654310234510772e-5,"lv":6.329831384551712e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"vi":3.23434515179523e-5,"lt":3.1760653355571936e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:48:09] local.INFO: Free search completed {"query":"روابط لمنصات ذكاء صناعي","results_count":5} 
[2025-07-11 15:48:09] local.ERROR: Unsupported operand types: string + int {"exception":"[object] (TypeError(code: 0): Unsupported operand types: string + int at C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php:512)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(405): App\\Services\\ModelMergerService->handleSearch('\\xD8\\xB1\\xD9\\x88\\xD8\\xA7\\xD8\\xA8\\xD8\\xB7 \\xD9\\x84\\xD9\\x85...', Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(61): App\\Services\\ModelMergerService->detectAndProcessCommands('\\xD8\\xA7\\xD8\\xA8\\xD8\\xAD\\xD8\\xAB \\xD8\\xB9\\xD9\\x86 \\xD8...', Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(96): App\\Services\\ModelMergerService->processMessage('\\xD8\\xA7\\xD8\\xA8\\xD8\\xAD\\xD8\\xAB \\xD8\\xB9\\xD9\\x86 \\xD8...', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(Illuminate\\Http\\Request))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}
"} 
[2025-07-11 15:50:20] local.INFO: Language detection {"message":"ماذا ترى","detected_code":"fa","max_score":0.11357121592926246,"result":{"fa":0.11357121592926246,"ar":0.11246759152391275,"ur":0.10738153551575072,"so":0.0007294938917975567,"he":0.00020314922949498678,"tr":7.585657312061947e-5,"et":6.434329421023287e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"ro":3.506179351499693e-5,"ko":3.429115991464593e-5,"lv":3.0127562839933627e-5,"sl":2.8015251524946096e-5,"vi":1.7137537158470558e-5,"lt":1.4292294010007371e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 15:51:38] local.ERROR: Live search error {"query":"test query","error":"Google Search API credentials not configured","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\LiveSearchService.php(46): App\\Services\\LiveSearchService->searchGoogle('test query', 3, 'en', 'SA')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(104): App\\Services\\LiveSearchService->search('test query', Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(90): App\\Console\\Commands\\TestAdvancedFeatures->testLiveSearch()
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(56): App\\Console\\Commands\\TestAdvancedFeatures->testAllFeatures()
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\TestAdvancedFeatures->handle()
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\TestAdvancedFeatures), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#18 {main}"} 
[2025-07-11 15:51:38] local.INFO: Image generation started {"prompt":"test image","provider":"openai","options":{"provider":"openai","size":"1024x1024"}} 
[2025-07-11 15:51:38] local.ERROR: Image generation error {"prompt":"test image","error":"OpenAI API key not configured","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ImageGenerationService.php(43): App\\Services\\ImageGenerationService->generateWithOpenAI('test image', '1024x1024', 'standard', 'natural', 1)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(131): App\\Services\\ImageGenerationService->generateImage('test image', Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(91): App\\Console\\Commands\\TestAdvancedFeatures->testImageGeneration()
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(56): App\\Console\\Commands\\TestAdvancedFeatures->testAllFeatures()
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\TestAdvancedFeatures->handle()
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\TestAdvancedFeatures), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#18 {main}"} 
[2025-07-11 15:51:38] local.INFO: Text-to-speech started {"text_length":21,"provider":"openai","voice":"default","language":"en"} 
[2025-07-11 15:51:38] local.ERROR: Text-to-speech error {"text":"Hello, this is a test","error":"OpenAI API key not configured","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\VoiceService.php(45): App\\Services\\VoiceService->ttsOpenAI('Hello, this is ...', 'default', Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(156): App\\Services\\VoiceService->textToSpeech('Hello, this is ...', Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(92): App\\Console\\Commands\\TestAdvancedFeatures->testVoiceServices()
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(56): App\\Console\\Commands\\TestAdvancedFeatures->testAllFeatures()
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\TestAdvancedFeatures->handle()
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\TestAdvancedFeatures), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#18 {main}"} 
[2025-07-11 15:51:38] local.INFO: Deep search started {"query":"artificial intelligence","options":{"language":"en"}} 
[2025-07-11 15:51:57] local.ERROR: App\Services\LiveSearchService::search(): Argument #1 ($query) must be of type string, array given, called in C:\Users\<USER>\Desktop\widdx-ai\app\Services\DeepSearchService.php on line 151 {"exception":"[object] (TypeError(code: 0): App\\Services\\LiveSearchService::search(): Argument #1 ($query) must be of type string, array given, called in C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\DeepSearchService.php on line 151 at C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\LiveSearchService.php:29)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\DeepSearchService.php(151): App\\Services\\LiveSearchService->search(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\DeepSearchService.php(47): App\\Services\\DeepSearchService->performMultipleSearches(Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(176): App\\Services\\DeepSearchService->deepSearch('artificial inte...', Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(93): App\\Console\\Commands\\TestAdvancedFeatures->testDeepSearch()
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(56): App\\Console\\Commands\\TestAdvancedFeatures->testAllFeatures()
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\TestAdvancedFeatures->handle()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\TestAdvancedFeatures), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#19 {main}
"} 
[2025-07-11 15:56:54] local.INFO: Free search completed {"query":"artificial intelligence","results_count":3} 
[2025-07-11 15:56:55] local.INFO: Free search completed {"query":"أخبار السعودية","results_count":5} 
[2025-07-11 15:56:55] local.INFO: Free search completed {"query":"الطقس اليوم","results_count":5} 
[2025-07-11 15:56:56] local.INFO: Free search completed {"query":"أسعار الذهب","results_count":5} 
[2025-07-11 15:56:56] local.INFO: Free search completed {"query":"مباريات اليوم","results_count":5} 
[2025-07-11 15:56:57] local.INFO: Free search completed {"query":"أخبار التقنية","results_count":5} 
[2025-07-11 15:57:06] local.INFO: Free image description generation started {"prompt":"beautiful sunset over mountains","options":{"style":"realistic","detail":"high"}} 
[2025-07-11 15:57:26] local.INFO: Free TTS instructions generated {"text_length":43,"language":"ar"} 
[2025-07-11 15:57:39] local.INFO: Think mode processing started {"message_length":20,"history_count":0} 
[2025-07-11 16:04:40] local.INFO: Language detection {"message":"مرحبا","detected_code":"ar","max_score":0.10665806315835791,"result":{"ar":0.10665806315835791,"fa":0.10429469856083184,"ur":0.09534008946245603,"so":0.0009493891797556719,"he":0.00024660361013562564,"et":8.336394469270356e-5,"tr":7.585657312061947e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.5951028945866016e-5,"id":4.409251662944479e-5,"ko":4.0264005391582206e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"lv":3.423586686356094e-5,"sl":3.357427000042656e-5,"lt":1.7519586205815487e-5,"vi":1.7137537158470558e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 16:04:50] local.INFO: Free search cache hit {"query":"artificial intelligence"} 
[2025-07-11 16:04:51] local.INFO: Free image description generation started {"prompt":"beautiful sunset over mountains","options":{"style":"realistic","detail":"high"}} 
[2025-07-11 16:05:25] local.INFO: Free TTS instructions generated {"text_length":43,"language":"ar"} 
[2025-07-11 16:05:25] local.INFO: Think mode processing started {"message_length":20,"history_count":0} 
[2025-07-11 16:05:44] local.INFO: Language detection {"message":"ارسم منظر طبيعي جميل\"","detected_code":"ar","max_score":0.2487318651031976,"result":{"ar":0.2487318651031976,"fa":0.22866626973462326,"ur":0.22542497102182005,"so":0.0018324607329842932,"he":0.00042236752683104314,"tr":0.00019617067362140859,"et":0.00015788625888769612,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":8.707526913691417e-5,"ko":7.722537857827023e-5,"lv":6.14724009461272e-5,"bn":5.912372307715048e-5,"sl":5.856233324664567e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"vi":3.23434515179523e-5,"lt":3.1760653355571936e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 16:05:44] local.INFO: Free image description generation started {"prompt":"منظر طبيعي جميل\"","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"arabic","target_language_code":"ar","language_confidence":0.2487318651031976,"max_tokens":2000,"temperature":0.7,"think_mode":false}} 
[2025-07-11 16:05:54] local.ERROR: Chat Controller Error {"message":"Undefined array key \"processing_time\"","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 121)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(121): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 121)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(Illuminate\\Http\\Request))
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#42 {main}","request_data":{"message":"ارسم منظر طبيعي جميل\"","session_id":"session_1752249592527_ldgeqc1gl","personality":"neutral"}} 
[2025-07-11 16:08:22] local.INFO: Think mode processing completed {"steps_count":5,"complexity_level":1,"success":true} 
[2025-07-11 16:10:58] local.INFO: Language detection {"message":"مرحبا قم بإنشاء مظهر لجبل اسمه حريش","detected_code":"ar","max_score":0.28790895266555405,"result":{"ar":0.28790895266555405,"fa":0.28667870007650753,"ur":0.2436170370386908,"so":0.002167539267015707,"he":0.0004764591777326717,"tr":0.00019617067362140859,"et":0.000178467197105057,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":9.055055704038304e-5,"ko":8.460359946154447e-5,"lv":6.710229905257945e-5,"sl":6.472678937787153e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"vi":3.6184236385709134e-5,"tl":3.5574798256092454e-5,"lt":3.1760653355571936e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 16:11:30] local.INFO: Gemini response successful {"attempt":1} 
[2025-07-11 16:11:46] local.INFO: Language detection {"message":"قم بتوليد الصورة","detected_code":"ar","max_score":0.3052992784712724,"result":{"ar":0.3052992784712724,"fa":0.22696129542664484,"ur":0.21533421162606,"so":0.0024467713787085514,"he":0.0005383816701455821,"et":0.00019540746394100745,"tr":0.0001607733849069996,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":9.977937713959479e-5,"ko":9.858708475460704e-5,"lv":8.094880520628632e-5,"sl":7.694562206655136e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"vi":3.640884368791714e-5,"tl":3.5574798256092454e-5,"lt":2.8021092557254597e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 16:12:16] local.INFO: Gemini response successful {"attempt":1} 
