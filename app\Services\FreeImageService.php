<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class FreeImageService
{
    private GeminiClient $geminiClient;
    private int $timeout;

    public function __construct(GeminiClient $geminiClient)
    {
        $this->geminiClient = $geminiClient;
        $this->timeout = 60;
    }

    /**
     * Generate image description using Gemini (free alternative to image generation)
     */
    public function generateImageDescription(string $prompt, array $options = []): array
    {
        try {
            Log::info('Free image description generation started', [
                'prompt' => $prompt,
                'options' => $options,
            ]);

            // Enhance the prompt for better image descriptions
            $enhancedPrompt = $this->enhancePromptForDescription($prompt, $options);

            // Use Gemini to generate detailed image description
            $response = $this->geminiClient->chat([
                ['role' => 'user', 'content' => $enhancedPrompt]
            ], [
                'max_tokens' => 800,
                'temperature' => 0.8,
            ]);

            if ($response['success']) {
                // Generate ASCII art or text-based representation
                $asciiArt = $this->generateAsciiArt($prompt);
                
                // Create a mock image file with the description
                $imagePath = $this->createDescriptionImage($response['content'], $prompt);

                return [
                    'success' => true,
                    'provider' => 'gemini_description',
                    'prompt' => $prompt,
                    'description' => $response['content'],
                    'ascii_art' => $asciiArt,
                    'image_path' => $imagePath,
                    'metadata' => [
                        'type' => 'description',
                        'enhanced_prompt' => $enhancedPrompt,
                        'free_alternative' => true,
                    ],
                ];
            } else {
                throw new \Exception('فشل في إنشاء وصف الصورة: ' . ($response['error'] ?? 'خطأ غير معروف'));
            }

        } catch (\Exception $e) {
            Log::error('Free image description error', [
                'prompt' => $prompt,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'prompt' => $prompt,
                'description' => '',
            ];
        }
    }

    /**
     * Generate SVG image using code (free alternative)
     */
    public function generateSvgImage(string $prompt, array $options = []): array
    {
        try {
            // Use Gemini to generate SVG code
            $svgPrompt = "Create an SVG image based on this description: '{$prompt}'. 
            Generate clean, valid SVG code that represents the described image. 
            The SVG should be 400x400 pixels and include appropriate colors and shapes.
            Return only the SVG code without any explanations.";

            $response = $this->geminiClient->chat([
                ['role' => 'user', 'content' => $svgPrompt]
            ], [
                'max_tokens' => 1000,
                'temperature' => 0.7,
            ]);

            if ($response['success']) {
                // Extract SVG code from response
                $svgCode = $this->extractSvgCode($response['content']);
                
                if ($svgCode) {
                    // Save SVG file
                    $filename = 'generated_svg_' . uniqid() . '.svg';
                    $path = 'generated_images/' . $filename;
                    
                    Storage::disk('public')->put($path, $svgCode);
                    
                    return [
                        'success' => true,
                        'provider' => 'gemini_svg',
                        'prompt' => $prompt,
                        'image_path' => $path,
                        'svg_code' => $svgCode,
                        'metadata' => [
                            'type' => 'svg',
                            'size' => '400x400',
                            'free_alternative' => true,
                        ],
                    ];
                }
            }

            // Fallback to simple SVG generation
            return $this->generateSimpleSvg($prompt);

        } catch (\Exception $e) {
            Log::error('SVG generation error', ['error' => $e->getMessage()]);
            return $this->generateSimpleSvg($prompt);
        }
    }

    /**
     * Generate placeholder image with text
     */
    public function generatePlaceholderImage(string $prompt, array $options = []): array
    {
        try {
            $width = $options['width'] ?? 400;
            $height = $options['height'] ?? 400;
            $backgroundColor = $options['bg_color'] ?? '#f0f0f0';
            $textColor = $options['text_color'] ?? '#333333';

            // Create SVG placeholder
            $svgContent = $this->createPlaceholderSvg($prompt, $width, $height, $backgroundColor, $textColor);
            
            // Save the SVG
            $filename = 'placeholder_' . uniqid() . '.svg';
            $path = 'generated_images/' . $filename;
            
            Storage::disk('public')->put($path, $svgContent);

            return [
                'success' => true,
                'provider' => 'placeholder',
                'prompt' => $prompt,
                'image_path' => $path,
                'metadata' => [
                    'type' => 'placeholder',
                    'size' => "{$width}x{$height}",
                    'free_alternative' => true,
                ],
            ];

        } catch (\Exception $e) {
            Log::error('Placeholder generation error', ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'prompt' => $prompt,
            ];
        }
    }

    /**
     * Enhance prompt for better image descriptions
     */
    private function enhancePromptForDescription(string $prompt, array $options): string
    {
        $style = $options['style'] ?? 'realistic';
        $detail = $options['detail'] ?? 'medium';

        $enhancedPrompt = "أريد وصفاً تفصيلياً لصورة بناءً على هذا الطلب: '{$prompt}'\n\n";
        $enhancedPrompt .= "يرجى تقديم وصف شامل يتضمن:\n";
        $enhancedPrompt .= "1. العناصر الرئيسية في الصورة\n";
        $enhancedPrompt .= "2. الألوان والإضاءة\n";
        $enhancedPrompt .= "3. التركيب والزوايا\n";
        $enhancedPrompt .= "4. الأجواء والمشاعر\n";
        $enhancedPrompt .= "5. التفاصيل الدقيقة\n\n";
        $enhancedPrompt .= "الأسلوب المطلوب: {$style}\n";
        $enhancedPrompt .= "مستوى التفصيل: {$detail}";

        return $enhancedPrompt;
    }

    /**
     * Generate simple ASCII art
     */
    private function generateAsciiArt(string $prompt): string
    {
        // Simple ASCII art based on keywords
        $keywords = strtolower($prompt);
        
        if (strpos($keywords, 'sun') !== false || strpos($keywords, 'شمس') !== false) {
            return "    \\   |   /\n     .-.-.-.  \n  ---|  O  |---\n     '-'-'-'  \n    /   |   \\";
        } elseif (strpos($keywords, 'tree') !== false || strpos($keywords, 'شجرة') !== false) {
            return "       /\\\n      /  \\\n     /____\\\n       ||\n       ||";
        } elseif (strpos($keywords, 'house') !== false || strpos($keywords, 'منزل') !== false) {
            return "      /\\\n     /  \\\n    /____\\\n    |    |\n    | [] |\n    |____|";
        } else {
            return "   ┌─────────┐\n   │  IMAGE  │\n   │ CONTENT │\n   │  HERE   │\n   └─────────┘";
        }
    }

    /**
     * Create description image (text-based image)
     */
    private function createDescriptionImage(string $description, string $prompt): string
    {
        $svgContent = $this->createTextImageSvg($description, $prompt);
        
        $filename = 'description_' . uniqid() . '.svg';
        $path = 'generated_images/' . $filename;
        
        Storage::disk('public')->put($path, $svgContent);
        
        return $path;
    }

    /**
     * Extract SVG code from Gemini response
     */
    private function extractSvgCode(string $content): ?string
    {
        // Look for SVG tags in the response
        if (preg_match('/<svg[^>]*>.*?<\/svg>/s', $content, $matches)) {
            return $matches[0];
        }
        
        // If no complete SVG found, try to extract and fix
        if (strpos($content, '<svg') !== false) {
            // Try to extract and create valid SVG
            $lines = explode("\n", $content);
            $svgLines = [];
            $inSvg = false;
            
            foreach ($lines as $line) {
                if (strpos($line, '<svg') !== false) {
                    $inSvg = true;
                }
                if ($inSvg) {
                    $svgLines[] = $line;
                }
                if (strpos($line, '</svg>') !== false) {
                    break;
                }
            }
            
            return implode("\n", $svgLines);
        }
        
        return null;
    }

    /**
     * Generate simple SVG as fallback
     */
    private function generateSimpleSvg(string $prompt): array
    {
        $svgContent = $this->createSimpleSvg($prompt);
        
        $filename = 'simple_' . uniqid() . '.svg';
        $path = 'generated_images/' . $filename;
        
        Storage::disk('public')->put($path, $svgContent);

        return [
            'success' => true,
            'provider' => 'simple_svg',
            'prompt' => $prompt,
            'image_path' => $path,
            'metadata' => [
                'type' => 'simple_svg',
                'free_alternative' => true,
            ],
        ];
    }

    /**
     * Create placeholder SVG
     */
    private function createPlaceholderSvg(string $text, int $width, int $height, string $bgColor, string $textColor): string
    {
        $fontSize = min($width, $height) / 20;
        $wrappedText = wordwrap($text, 20, "\n");
        $lines = explode("\n", $wrappedText);
        
        $svg = "<svg width='{$width}' height='{$height}' xmlns='http://www.w3.org/2000/svg'>";
        $svg .= "<rect width='100%' height='100%' fill='{$bgColor}' stroke='#ccc' stroke-width='2'/>";
        
        $y = $height / 2 - (count($lines) * $fontSize / 2);
        foreach ($lines as $line) {
            $svg .= "<text x='50%' y='{$y}' font-family='Arial' font-size='{$fontSize}' fill='{$textColor}' text-anchor='middle'>" . htmlspecialchars($line) . "</text>";
            $y += $fontSize + 5;
        }
        
        $svg .= "</svg>";
        
        return $svg;
    }

    /**
     * Create text-based image SVG
     */
    private function createTextImageSvg(string $description, string $prompt): string
    {
        $svg = "<svg width='600' height='400' xmlns='http://www.w3.org/2000/svg'>";
        $svg .= "<rect width='100%' height='100%' fill='#f8f9fa' stroke='#dee2e6' stroke-width='2'/>";
        
        // Title
        $svg .= "<text x='300' y='30' font-family='Arial' font-size='18' font-weight='bold' fill='#495057' text-anchor='middle'>وصف الصورة المطلوبة</text>";
        
        // Prompt
        $svg .= "<text x='300' y='60' font-family='Arial' font-size='14' fill='#6c757d' text-anchor='middle'>" . htmlspecialchars($prompt) . "</text>";
        
        // Description (wrapped)
        $wrappedDesc = wordwrap($description, 60, "\n");
        $lines = array_slice(explode("\n", $wrappedDesc), 0, 15); // Limit to 15 lines
        
        $y = 100;
        foreach ($lines as $line) {
            $svg .= "<text x='50' y='{$y}' font-family='Arial' font-size='12' fill='#212529'>" . htmlspecialchars($line) . "</text>";
            $y += 18;
        }
        
        $svg .= "</svg>";
        
        return $svg;
    }

    /**
     * Create simple SVG based on prompt
     */
    private function createSimpleSvg(string $prompt): string
    {
        $svg = "<svg width='400' height='400' xmlns='http://www.w3.org/2000/svg'>";
        $svg .= "<rect width='100%' height='100%' fill='#e3f2fd'/>";
        
        // Add some basic shapes based on keywords
        $keywords = strtolower($prompt);
        
        if (strpos($keywords, 'circle') !== false || strpos($keywords, 'دائرة') !== false) {
            $svg .= "<circle cx='200' cy='200' r='100' fill='#2196f3' stroke='#1976d2' stroke-width='3'/>";
        } elseif (strpos($keywords, 'square') !== false || strpos($keywords, 'مربع') !== false) {
            $svg .= "<rect x='150' y='150' width='100' height='100' fill='#4caf50' stroke='#388e3c' stroke-width='3'/>";
        } else {
            // Default: simple geometric pattern
            $svg .= "<rect x='100' y='100' width='200' height='200' fill='#ff9800' stroke='#f57c00' stroke-width='3'/>";
            $svg .= "<circle cx='200' cy='200' r='50' fill='#fff'/>";
        }
        
        // Add text
        $svg .= "<text x='200' y='350' font-family='Arial' font-size='16' fill='#333' text-anchor='middle'>" . htmlspecialchars(substr($prompt, 0, 30)) . "</text>";
        
        $svg .= "</svg>";
        
        return $svg;
    }

    /**
     * Get available free options
     */
    public function getAvailableOptions(): array
    {
        return [
            'providers' => ['gemini_description', 'gemini_svg', 'placeholder', 'simple_svg'],
            'types' => ['description', 'svg', 'placeholder'],
            'styles' => ['realistic', 'artistic', 'simple', 'abstract'],
            'sizes' => ['400x400', '600x400', '800x600'],
            'free_alternative' => true,
            'note' => 'هذه بدائل مجانية لتوليد الصور. للحصول على صور حقيقية، يرجى إضافة مفاتيح API للخدمات المدفوعة.',
        ];
    }
}
