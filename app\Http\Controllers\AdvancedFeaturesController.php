<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use App\Services\LiveSearchService;
use App\Services\ImageGenerationService;
use App\Services\VoiceService;
use App\Services\DeepSearchService;
use App\Services\ThinkModeService;
use App\Services\DocumentAnalysisService;
use App\Services\VisionService;

class AdvancedFeaturesController extends Controller
{
    private LiveSearchService $liveSearch;
    private ImageGenerationService $imageGeneration;
    private VoiceService $voice;
    private DeepSearchService $deepSearch;
    private ThinkModeService $thinkMode;
    private DocumentAnalysisService $documentAnalysis;
    private VisionService $vision;

    // Free alternatives
    private FreeSearchService $freeSearch;
    private FreeImageService $freeImage;
    private FreeVoiceService $freeVoice;

    public function __construct(
        LiveSearchService $liveSearch,
        ImageGenerationService $imageGeneration,
        VoiceService $voice,
        DeepSearchService $deepSearch,
        ThinkModeService $thinkMode,
        DocumentAnalysisService $documentAnalysis,
        VisionService $vision,
        FreeSearchService $freeSearch,
        FreeImageService $freeImage,
        FreeVoiceService $freeVoice
    ) {
        $this->liveSearch = $liveSearch;
        $this->imageGeneration = $imageGeneration;
        $this->voice = $voice;
        $this->deepSearch = $deepSearch;
        $this->thinkMode = $thinkMode;
        $this->documentAnalysis = $documentAnalysis;
        $this->vision = $vision;
        $this->freeSearch = $freeSearch;
        $this->freeImage = $freeImage;
        $this->freeVoice = $freeVoice;
    }

    /**
     * Perform live search (with free alternatives)
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'query' => 'required|string|max:500',
                'provider' => 'sometimes|string|in:google,bing,serp,duckduckgo,free',
                'max_results' => 'sometimes|integer|min:1|max:20',
                'language' => 'sometimes|string|max:10',
                'region' => 'sometimes|string|max:10',
                'use_free' => 'sometimes|boolean',
            ]);

            $useFree = $request->boolean('use_free', true);
            $provider = $request->provider ?? 'duckduckgo';

            if ($useFree || $provider === 'duckduckgo' || $provider === 'free') {
                $result = $this->freeSearch->search($request->query, [
                    'max_results' => $request->max_results ?? 10,
                    'language' => $request->language ?? 'ar',
                    'region' => $request->region ?? 'SA',
                ]);
            } else {
                $result = $this->liveSearch->search($request->query, [
                    'provider' => $provider,
                    'max_results' => $request->max_results ?? 10,
                    'language' => $request->language ?? 'ar',
                    'region' => $request->region ?? 'SA',
                ]);
            }

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Search API error', [
                'query' => $request->query,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'فشل في تنفيذ البحث',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get free search suggestions
     */
    public function searchSuggestions(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'query' => 'required|string|max:100',
            ]);

            $result = $this->freeSearch->getSuggestions($request->query);
            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'فشل في جلب الاقتراحات',
                'suggestions' => [],
            ], 500);
        }
    }

    /**
     * Generate image from text (with free alternatives)
     */
    public function generateImage(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'prompt' => 'required|string|max:1000',
                'provider' => 'sometimes|string|in:openai,stability,midjourney,free,description,svg',
                'size' => 'sometimes|string',
                'quality' => 'sometimes|string|in:standard,hd',
                'style' => 'sometimes|string|in:natural,vivid,realistic,artistic',
                'count' => 'sometimes|integer|min:1|max:4',
                'use_free' => 'sometimes|boolean',
            ]);

            $useFree = $request->boolean('use_free', false); // Default to real image generation
            $provider = $request->provider ?? 'gemini'; // Use Gemini as default

            if ($useFree || in_array($provider, ['free', 'description', 'svg'])) {
                // Use free alternatives
                if ($provider === 'svg') {
                    $result = $this->freeImage->generateSvgImage($request->prompt, [
                        'style' => $request->style ?? 'simple',
                    ]);
                } else {
                    $result = $this->freeImage->generateImageDescription($request->prompt, [
                        'style' => $request->style ?? 'realistic',
                        'detail' => 'high',
                    ]);
                }
            } else {
                // Use paid services
                $result = $this->imageGeneration->generateImage($request->prompt, [
                    'provider' => $provider,
                    'size' => $request->size ?? '1024x1024',
                    'quality' => $request->quality ?? 'standard',
                    'style' => $request->style ?? 'natural',
                    'count' => $request->count ?? 1,
                ]);
            }

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Image generation API error', [
                'prompt' => $request->prompt,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'فشل في إنشاء الصورة',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate placeholder image (free)
     */
    public function generatePlaceholder(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'text' => 'required|string|max:200',
                'width' => 'sometimes|integer|min:100|max:1200',
                'height' => 'sometimes|integer|min:100|max:1200',
                'bg_color' => 'sometimes|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'text_color' => 'sometimes|string|regex:/^#[0-9A-Fa-f]{6}$/',
            ]);

            $result = $this->freeImage->generatePlaceholderImage($request->text, [
                'width' => $request->width ?? 400,
                'height' => $request->height ?? 400,
                'bg_color' => $request->bg_color ?? '#f0f0f0',
                'text_color' => $request->text_color ?? '#333333',
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'فشل في إنشاء الصورة التجريبية',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Convert text to speech (with free alternatives)
     */
    public function textToSpeech(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'text' => 'required|string|max:5000',
                'provider' => 'sometimes|string|in:elevenlabs,openai,azure,browser,free',
                'voice' => 'sometimes|string',
                'language' => 'sometimes|string|max:10',
                'speed' => 'sometimes|numeric|min:0.5|max:2.0',
                'use_free' => 'sometimes|boolean',
            ]);

            $useFree = $request->boolean('use_free', true);
            $provider = $request->provider ?? 'browser';

            if ($useFree || $provider === 'browser' || $provider === 'free') {
                // Use free browser-based TTS
                $result = $this->freeVoice->generateTtsInstructions($request->text, [
                    'language' => $request->language ?? 'ar',
                    'voice' => $request->voice ?? 'default',
                    'speed' => $request->speed ?? 1.0,
                    'pitch' => 1.0,
                ]);
            } else {
                // Use paid services
                $result = $this->voice->textToSpeech($request->text, [
                    'provider' => $provider,
                    'voice' => $request->voice ?? 'default',
                    'language' => $request->language ?? 'ar',
                    'speed' => $request->speed ?? 1.0,
                ]);
            }

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('TTS API error', [
                'text_length' => strlen($request->text),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'فشل في تحويل النص إلى كلام',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get speech-to-text instructions (free)
     */
    public function speechToTextInstructions(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'language' => 'sometimes|string|max:10',
                'continuous' => 'sometimes|boolean',
            ]);

            $result = $this->freeVoice->generateSttInstructions([
                'language' => $request->language ?? 'ar',
                'continuous' => $request->boolean('continuous', false),
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'فشل في إنشاء تعليمات التعرف على الكلام',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate voice widget (free)
     */
    public function voiceWidget(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'language' => 'sometimes|string|max:10',
                'theme' => 'sometimes|string|in:dark,light',
            ]);

            $result = $this->freeVoice->generateVoiceWidget([
                'language' => $request->language ?? 'ar',
                'theme' => $request->theme ?? 'dark',
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'فشل في إنشاء عنصر التحكم الصوتي',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Convert speech to text
     */
    public function speechToText(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'audio' => 'required|file|mimes:mp3,wav,m4a,ogg|max:25600', // 25MB
                'provider' => 'sometimes|string|in:openai,azure',
                'language' => 'sometimes|string|max:10',
            ]);

            $audioFile = $request->file('audio');
            $savedPath = $audioFile->store('temp_audio');

            $result = $this->voice->speechToText($savedPath, [
                'provider' => $request->provider ?? 'openai',
                'language' => $request->language ?? 'ar',
            ]);

            // Clean up temporary file
            \Storage::delete($savedPath);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('STT API error', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'فشل في تحويل الكلام إلى نص',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Perform deep search
     */
    public function deepSearch(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'query' => 'required|string|max:500',
                'language' => 'sometimes|string|max:10',
                'region' => 'sometimes|string|max:10',
            ]);

            $result = $this->deepSearch->deepSearch($request->query, [
                'language' => $request->language ?? 'ar',
                'region' => $request->region ?? 'SA',
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Deep search API error', [
                'query' => $request->query,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'فشل في البحث العميق',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Process with thinking mode
     */
    public function thinkMode(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'message' => 'required|string|max:2000',
                'conversation_history' => 'sometimes|array',
                'max_tokens' => 'sometimes|integer|min:100|max:2000',
                'temperature' => 'sometimes|numeric|min:0|max:2',
            ]);

            $result = $this->thinkMode->processWithThinking(
                $request->message,
                $request->conversation_history ?? [],
                [
                    'max_tokens' => $request->max_tokens ?? 1000,
                    'temperature' => $request->temperature ?? 0.7,
                ]
            );

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Think mode API error', [
                'message' => substr($request->message, 0, 100),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'فشل في معالجة وضع التفكير',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Analyze document
     */
    public function analyzeDocument(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'document' => 'required|file|mimes:pdf,docx,doc,txt,rtf,xlsx,xls,csv|max:10240', // 10MB
                'analysis_type' => 'sometimes|string|in:comprehensive,quick,detailed',
                'summary_length' => 'sometimes|string|in:short,medium,long',
            ]);

            $document = $request->file('document');

            $result = $this->documentAnalysis->analyzeDocument($document, [
                'analysis_type' => $request->analysis_type ?? 'comprehensive',
                'summary_length' => $request->summary_length ?? 'medium',
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Document analysis API error', [
                'filename' => $request->file('document')?->getClientOriginalName(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'فشل في تحليل المستند',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Analyze image
     */
    public function analyzeImage(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'image' => 'required|file|mimes:jpg,jpeg,png,gif,webp,bmp|max:20480', // 20MB
                'prompt' => 'sometimes|string|max:1000',
                'analysis_type' => 'sometimes|string|in:comprehensive,quick,detailed',
                'extract_text' => 'sometimes|boolean',
                'detect_objects' => 'sometimes|boolean',
            ]);

            $image = $request->file('image');

            $result = $this->vision->analyzeImage($image, $request->prompt ?? '', [
                'analysis_type' => $request->analysis_type ?? 'comprehensive',
                'extract_text' => $request->extract_text ?? false,
                'detect_objects' => $request->detect_objects ?? false,
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Image analysis API error', [
                'filename' => $request->file('image')?->getClientOriginalName(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'فشل في تحليل الصورة',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available capabilities
     */
    public function getCapabilities(): JsonResponse
    {
        return response()->json([
            'live_search' => [
                'providers' => ['google', 'bing', 'serp'],
                'max_results' => 20,
                'supported_languages' => ['ar', 'en'],
            ],
            'image_generation' => $this->imageGeneration->getAvailableOptions(),
            'voice' => [
                'tts_providers' => ['elevenlabs', 'openai', 'azure'],
                'stt_providers' => ['openai', 'azure'],
                'supported_formats' => ['mp3', 'wav', 'm4a', 'ogg'],
                'voices' => $this->voice->getAvailableVoices(),
            ],
            'document_analysis' => $this->documentAnalysis->getAnalysisOptions(),
            'vision' => $this->vision->getCapabilities(),
            'deep_search' => [
                'max_sources' => 10,
                'supported_languages' => ['ar', 'en'],
            ],
            'think_mode' => [
                'max_steps' => 10,
                'complexity_levels' => [1, 2, 3, 4, 5],
            ],
        ]);
    }
}
