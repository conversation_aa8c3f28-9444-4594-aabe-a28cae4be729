<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ImageGenerationService
{
    private array $providers;
    private int $timeout;

    public function __construct()
    {
        $this->providers = [
            'openai' => config('services.image_generation.openai'),
            'stability' => config('services.image_generation.stability'),
            'midjourney' => config('services.image_generation.midjourney'),
            'gemini' => config('services.gemini'),
        ];
        $this->timeout = 120; // Image generation takes longer
    }

    /**
     * Generate image from text prompt
     */
    public function generateImage(string $prompt, array $options = []): array
    {
        try {
            $provider = $options['provider'] ?? 'openai';
            $size = $options['size'] ?? '1024x1024';
            $quality = $options['quality'] ?? 'standard';
            $style = $options['style'] ?? 'natural';
            $count = $options['count'] ?? 1;

            Log::info('Image generation started', [
                'prompt' => $prompt,
                'provider' => $provider,
                'options' => $options,
            ]);

            $result = match($provider) {
                'openai' => $this->generateWithOpenAI($prompt, $size, $quality, $style, $count),
                'stability' => $this->generateWithStability($prompt, $options),
                'midjourney' => $this->generateWithMidjourney($prompt, $options),
                'gemini' => $this->generateWithGemini($prompt, $options),
                default => $this->generateWithGemini($prompt, $options), // Use Gemini as default
            };

            Log::info('Image generation completed', [
                'prompt' => $prompt,
                'provider' => $provider,
                'success' => $result['success'],
                'images_count' => count($result['images'] ?? []),
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Image generation error', [
                'prompt' => $prompt,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'images' => [],
                'prompt' => $prompt,
            ];
        }
    }

    /**
     * Generate image using OpenAI DALL-E
     */
    private function generateWithOpenAI(string $prompt, string $size, string $quality, string $style, int $count): array
    {
        $config = $this->providers['openai'];

        if (!$config['api_key']) {
            throw new \Exception('OpenAI API key not configured');
        }

        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $config['api_key'],
                'Content-Type' => 'application/json',
            ])
            ->post($config['base_url'] . '/images/generations', [
                'model' => 'dall-e-3',
                'prompt' => $prompt,
                'n' => min($count, 1), // DALL-E 3 only supports 1 image at a time
                'size' => $size,
                'quality' => $quality,
                'style' => $style,
                'response_format' => 'url',
            ]);

        if (!$response->successful()) {
            throw new \Exception('OpenAI image generation failed: ' . $response->body());
        }

        $data = $response->json();
        $images = [];

        foreach ($data['data'] as $imageData) {
            $imageUrl = $imageData['url'];
            $savedPath = $this->saveImageFromUrl($imageUrl, 'openai');

            $images[] = [
                'url' => $imageUrl,
                'local_path' => $savedPath,
                'revised_prompt' => $imageData['revised_prompt'] ?? $prompt,
            ];
        }

        return [
            'success' => true,
            'provider' => 'openai',
            'prompt' => $prompt,
            'images' => $images,
            'metadata' => [
                'model' => 'dall-e-3',
                'size' => $size,
                'quality' => $quality,
                'style' => $style,
            ],
        ];
    }

    /**
     * Generate image using Stability AI
     */
    private function generateWithStability(string $prompt, array $options): array
    {
        $config = $this->providers['stability'];

        if (!$config['api_key']) {
            throw new \Exception('Stability AI API key not configured');
        }

        $width = $options['width'] ?? 1024;
        $height = $options['height'] ?? 1024;
        $steps = $options['steps'] ?? 30;
        $cfgScale = $options['cfg_scale'] ?? 7;

        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $config['api_key'],
                'Content-Type' => 'application/json',
            ])
            ->post($config['base_url'] . '/generation/stable-diffusion-xl-1024-v1-0/text-to-image', [
                'text_prompts' => [
                    ['text' => $prompt, 'weight' => 1],
                ],
                'width' => $width,
                'height' => $height,
                'steps' => $steps,
                'cfg_scale' => $cfgScale,
                'samples' => 1,
            ]);

        if (!$response->successful()) {
            throw new \Exception('Stability AI image generation failed: ' . $response->body());
        }

        $data = $response->json();
        $images = [];

        foreach ($data['artifacts'] as $artifact) {
            $imageData = base64_decode($artifact['base64']);
            $savedPath = $this->saveImageFromData($imageData, 'stability');

            $images[] = [
                'local_path' => $savedPath,
                'seed' => $artifact['seed'],
            ];
        }

        return [
            'success' => true,
            'provider' => 'stability',
            'prompt' => $prompt,
            'images' => $images,
            'metadata' => [
                'width' => $width,
                'height' => $height,
                'steps' => $steps,
                'cfg_scale' => $cfgScale,
            ],
        ];
    }

    /**
     * Generate image using Midjourney (placeholder - requires custom implementation)
     */
    private function generateWithMidjourney(string $prompt, array $options): array
    {
        // This would require a custom Midjourney API implementation
        // For now, we'll return a placeholder response

        return [
            'success' => false,
            'error' => 'Midjourney integration not yet implemented',
            'provider' => 'midjourney',
            'prompt' => $prompt,
            'images' => [],
        ];
    }

    /**
     * Generate image using Gemini 2.0 Flash Image Generation
     */
    private function generateWithGemini(string $prompt, array $options): array
    {
        $config = $this->providers['gemini'];

        if (!$config['api_key']) {
            throw new \Exception('Gemini API key not configured');
        }

        // Enhance prompt for better image generation
        $enhancedPrompt = $this->enhancePromptForGemini($prompt, $options);

        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($config['base_url'] . '/v1beta/models/gemini-2.0-flash-preview-image-generation:generateContent?key=' . $config['api_key'], [
                'contents' => [
                    [
                        'parts' => [
                            [
                                'text' => $enhancedPrompt
                            ]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'responseModalities' => ['TEXT', 'IMAGE'],
                    'maxOutputTokens' => 8192,
                    'temperature' => $options['temperature'] ?? 0.8,
                ],
            ]);

        if (!$response->successful()) {
            Log::error('Gemini image generation failed', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
            throw new \Exception('Gemini image generation failed: ' . $response->body());
        }

        $data = $response->json();
        $images = [];

        // Process Gemini response - it returns images in the response
        if (isset($data['candidates'][0]['content']['parts'])) {
            foreach ($data['candidates'][0]['content']['parts'] as $part) {
                if (isset($part['inlineData'])) {
                    // Handle inline image data
                    $imageData = base64_decode($part['inlineData']['data']);
                    $savedPath = $this->saveImageFromData($imageData, 'gemini');

                    $images[] = [
                        'url' => Storage::disk('public')->url($savedPath),
                        'local_path' => $savedPath,
                        'mime_type' => $part['inlineData']['mimeType'] ?? 'image/png',
                    ];
                }
            }
        }

        if (empty($images)) {
            throw new \Exception('No images generated by Gemini');
        }

        return [
            'success' => true,
            'provider' => 'gemini',
            'prompt' => $prompt,
            'enhanced_prompt' => $enhancedPrompt,
            'images' => $images,
            'metadata' => [
                'model' => 'gemini-2.0-flash-preview-image-generation',
                'temperature' => $options['temperature'] ?? 0.8,
            ],
        ];
    }

    /**
     * Save image from URL to local storage
     */
    private function saveImageFromUrl(string $url, string $provider): string
    {
        $imageContent = Http::timeout(60)->get($url)->body();
        return $this->saveImageFromData($imageContent, $provider);
    }

    /**
     * Save image data to local storage
     */
    private function saveImageFromData(string $imageData, string $provider): string
    {
        $filename = $provider . '_' . uniqid() . '.png';
        $path = 'generated_images/' . $filename;

        Storage::disk('public')->put($path, $imageData);

        return $path;
    }

    /**
     * Enhance prompt for Gemini image generation
     */
    private function enhancePromptForGemini(string $prompt, array $options): string
    {
        $style = $options['style'] ?? 'natural';
        $quality = $options['quality'] ?? 'standard';

        // Add style enhancements
        $styleEnhancements = [
            'natural' => 'realistic, natural lighting, high detail',
            'vivid' => 'vibrant colors, dramatic lighting, artistic',
            'photographic' => 'photorealistic, professional photography, sharp focus',
            'artistic' => 'artistic style, creative composition, expressive',
            'digital-art' => 'digital art style, modern, clean lines',
        ];

        $qualityEnhancements = [
            'standard' => 'good quality',
            'hd' => 'high resolution, ultra detailed, 4K quality',
        ];

        $enhancement = $styleEnhancements[$style] ?? $styleEnhancements['natural'];
        $qualityText = $qualityEnhancements[$quality] ?? $qualityEnhancements['standard'];

        // Create enhanced prompt
        $enhancedPrompt = "Generate an image: {$prompt}. Style: {$enhancement}. Quality: {$qualityText}.";

        // Add Arabic language support
        if (preg_match('/[\x{0600}-\x{06FF}]/u', $prompt)) {
            $enhancedPrompt .= " Note: This prompt contains Arabic text, please interpret and generate accordingly.";
        }

        return $enhancedPrompt;
    }

    /**
     * Get available image styles and options
     */
    public function getAvailableOptions(): array
    {
        return [
            'providers' => ['openai', 'stability', 'midjourney', 'gemini'],
            'sizes' => [
                'openai' => ['1024x1024', '1792x1024', '1024x1792'],
                'stability' => ['512x512', '768x768', '1024x1024', '1536x1536'],
                'gemini' => ['1024x1024', '1792x1024', '1024x1792'], // Gemini supports various sizes
            ],
            'styles' => [
                'openai' => ['natural', 'vivid'],
                'stability' => ['photographic', 'digital-art', 'comic-book', 'fantasy-art', 'line-art', 'analog-film', 'neon-punk', 'isometric'],
                'gemini' => ['natural', 'vivid', 'photographic', 'artistic', 'digital-art'],
            ],
            'quality' => ['standard', 'hd'],
        ];
    }

    /**
     * Enhance prompt for better image generation
     */
    public function enhancePrompt(string $prompt, string $style = 'natural'): string
    {
        $enhancements = [
            'natural' => 'high quality, detailed, realistic',
            'artistic' => 'artistic, creative, beautiful composition',
            'photographic' => 'professional photography, high resolution, sharp focus',
            'fantasy' => 'fantasy art, magical, ethereal, detailed',
            'anime' => 'anime style, manga, detailed character design',
            'digital-art' => 'digital art, concept art, detailed illustration',
        ];

        $enhancement = $enhancements[$style] ?? $enhancements['natural'];

        return $prompt . ', ' . $enhancement;
    }

    /**
     * Translate Arabic prompt to English for better results
     */
    public function translatePromptIfNeeded(string $prompt): string
    {
        // Simple check if prompt contains Arabic characters
        if (preg_match('/[\x{0600}-\x{06FF}]/u', $prompt)) {
            // In a real implementation, you would use a translation service
            // For now, we'll return the original prompt with a note
            return $prompt . ' (Arabic prompt - consider translating to English for better results)';
        }

        return $prompt;
    }
}
