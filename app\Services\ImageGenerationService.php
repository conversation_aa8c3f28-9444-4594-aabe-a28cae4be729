<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ImageGenerationService
{
    private array $providers;
    private int $timeout;

    public function __construct()
    {
        $this->providers = [
            'openai' => config('services.image_generation.openai'),
            'stability' => config('services.image_generation.stability'),
            'midjourney' => config('services.image_generation.midjourney'),
        ];
        $this->timeout = 120; // Image generation takes longer
    }

    /**
     * Generate image from text prompt
     */
    public function generateImage(string $prompt, array $options = []): array
    {
        try {
            $provider = $options['provider'] ?? 'openai';
            $size = $options['size'] ?? '1024x1024';
            $quality = $options['quality'] ?? 'standard';
            $style = $options['style'] ?? 'natural';
            $count = $options['count'] ?? 1;

            Log::info('Image generation started', [
                'prompt' => $prompt,
                'provider' => $provider,
                'options' => $options,
            ]);

            $result = match($provider) {
                'openai' => $this->generateWithOpenAI($prompt, $size, $quality, $style, $count),
                'stability' => $this->generateWithStability($prompt, $options),
                'midjourney' => $this->generateWithMidjourney($prompt, $options),
                default => $this->generateWithOpenAI($prompt, $size, $quality, $style, $count),
            };

            Log::info('Image generation completed', [
                'prompt' => $prompt,
                'provider' => $provider,
                'success' => $result['success'],
                'images_count' => count($result['images'] ?? []),
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Image generation error', [
                'prompt' => $prompt,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'images' => [],
                'prompt' => $prompt,
            ];
        }
    }

    /**
     * Generate image using OpenAI DALL-E
     */
    private function generateWithOpenAI(string $prompt, string $size, string $quality, string $style, int $count): array
    {
        $config = $this->providers['openai'];
        
        if (!$config['api_key']) {
            throw new \Exception('OpenAI API key not configured');
        }

        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $config['api_key'],
                'Content-Type' => 'application/json',
            ])
            ->post($config['base_url'] . '/images/generations', [
                'model' => 'dall-e-3',
                'prompt' => $prompt,
                'n' => min($count, 1), // DALL-E 3 only supports 1 image at a time
                'size' => $size,
                'quality' => $quality,
                'style' => $style,
                'response_format' => 'url',
            ]);

        if (!$response->successful()) {
            throw new \Exception('OpenAI image generation failed: ' . $response->body());
        }

        $data = $response->json();
        $images = [];

        foreach ($data['data'] as $imageData) {
            $imageUrl = $imageData['url'];
            $savedPath = $this->saveImageFromUrl($imageUrl, 'openai');
            
            $images[] = [
                'url' => $imageUrl,
                'local_path' => $savedPath,
                'revised_prompt' => $imageData['revised_prompt'] ?? $prompt,
            ];
        }

        return [
            'success' => true,
            'provider' => 'openai',
            'prompt' => $prompt,
            'images' => $images,
            'metadata' => [
                'model' => 'dall-e-3',
                'size' => $size,
                'quality' => $quality,
                'style' => $style,
            ],
        ];
    }

    /**
     * Generate image using Stability AI
     */
    private function generateWithStability(string $prompt, array $options): array
    {
        $config = $this->providers['stability'];
        
        if (!$config['api_key']) {
            throw new \Exception('Stability AI API key not configured');
        }

        $width = $options['width'] ?? 1024;
        $height = $options['height'] ?? 1024;
        $steps = $options['steps'] ?? 30;
        $cfgScale = $options['cfg_scale'] ?? 7;

        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $config['api_key'],
                'Content-Type' => 'application/json',
            ])
            ->post($config['base_url'] . '/generation/stable-diffusion-xl-1024-v1-0/text-to-image', [
                'text_prompts' => [
                    ['text' => $prompt, 'weight' => 1],
                ],
                'width' => $width,
                'height' => $height,
                'steps' => $steps,
                'cfg_scale' => $cfgScale,
                'samples' => 1,
            ]);

        if (!$response->successful()) {
            throw new \Exception('Stability AI image generation failed: ' . $response->body());
        }

        $data = $response->json();
        $images = [];

        foreach ($data['artifacts'] as $artifact) {
            $imageData = base64_decode($artifact['base64']);
            $savedPath = $this->saveImageFromData($imageData, 'stability');
            
            $images[] = [
                'local_path' => $savedPath,
                'seed' => $artifact['seed'],
            ];
        }

        return [
            'success' => true,
            'provider' => 'stability',
            'prompt' => $prompt,
            'images' => $images,
            'metadata' => [
                'width' => $width,
                'height' => $height,
                'steps' => $steps,
                'cfg_scale' => $cfgScale,
            ],
        ];
    }

    /**
     * Generate image using Midjourney (placeholder - requires custom implementation)
     */
    private function generateWithMidjourney(string $prompt, array $options): array
    {
        // This would require a custom Midjourney API implementation
        // For now, we'll return a placeholder response
        
        return [
            'success' => false,
            'error' => 'Midjourney integration not yet implemented',
            'provider' => 'midjourney',
            'prompt' => $prompt,
            'images' => [],
        ];
    }

    /**
     * Save image from URL to local storage
     */
    private function saveImageFromUrl(string $url, string $provider): string
    {
        $imageContent = Http::timeout(60)->get($url)->body();
        return $this->saveImageFromData($imageContent, $provider);
    }

    /**
     * Save image data to local storage
     */
    private function saveImageFromData(string $imageData, string $provider): string
    {
        $filename = $provider . '_' . uniqid() . '.png';
        $path = 'generated_images/' . $filename;
        
        Storage::disk('public')->put($path, $imageData);
        
        return $path;
    }

    /**
     * Get available image styles and options
     */
    public function getAvailableOptions(): array
    {
        return [
            'providers' => ['openai', 'stability', 'midjourney'],
            'sizes' => [
                'openai' => ['1024x1024', '1792x1024', '1024x1792'],
                'stability' => ['512x512', '768x768', '1024x1024', '1536x1536'],
            ],
            'styles' => [
                'openai' => ['natural', 'vivid'],
                'stability' => ['photographic', 'digital-art', 'comic-book', 'fantasy-art', 'line-art', 'analog-film', 'neon-punk', 'isometric'],
            ],
            'quality' => ['standard', 'hd'],
        ];
    }

    /**
     * Enhance prompt for better image generation
     */
    public function enhancePrompt(string $prompt, string $style = 'natural'): string
    {
        $enhancements = [
            'natural' => 'high quality, detailed, realistic',
            'artistic' => 'artistic, creative, beautiful composition',
            'photographic' => 'professional photography, high resolution, sharp focus',
            'fantasy' => 'fantasy art, magical, ethereal, detailed',
            'anime' => 'anime style, manga, detailed character design',
            'digital-art' => 'digital art, concept art, detailed illustration',
        ];

        $enhancement = $enhancements[$style] ?? $enhancements['natural'];
        
        return $prompt . ', ' . $enhancement;
    }

    /**
     * Translate Arabic prompt to English for better results
     */
    public function translatePromptIfNeeded(string $prompt): string
    {
        // Simple check if prompt contains Arabic characters
        if (preg_match('/[\x{0600}-\x{06FF}]/u', $prompt)) {
            // In a real implementation, you would use a translation service
            // For now, we'll return the original prompt with a note
            return $prompt . ' (Arabic prompt - consider translating to English for better results)';
        }
        
        return $prompt;
    }
}
