<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\LiveSearchService;
use App\Services\ImageGenerationService;
use App\Services\VoiceService;
use App\Services\DeepSearchService;
use App\Services\ThinkModeService;
use App\Services\DocumentAnalysisService;
use App\Services\VisionService;
use App\Services\FreeSearchService;
use App\Services\FreeImageService;
use App\Services\FreeVoiceService;

class TestAdvancedFeatures extends Command
{
    protected $signature = 'widdx:test-features {--feature=all : Specific feature to test (all, search, image, voice, deep-search, think, document, vision)}';
    protected $description = 'Test WIDDX AI advanced features';

    private LiveSearchService $liveSearch;
    private ImageGenerationService $imageGeneration;
    private VoiceService $voice;
    private DeepSearchService $deepSearch;
    private ThinkModeService $thinkMode;
    private DocumentAnalysisService $documentAnalysis;
    private VisionService $vision;

    public function __construct(
        LiveSearchService $liveSearch,
        ImageGenerationService $imageGeneration,
        VoiceService $voice,
        DeepSearchService $deepSearch,
        ThinkModeService $thinkMode,
        DocumentAnalysisService $documentAnalysis,
        VisionService $vision
    ) {
        parent::__construct();

        $this->liveSearch = $liveSearch;
        $this->imageGeneration = $imageGeneration;
        $this->voice = $voice;
        $this->deepSearch = $deepSearch;
        $this->thinkMode = $thinkMode;
        $this->documentAnalysis = $documentAnalysis;
        $this->vision = $vision;
    }

    public function handle()
    {
        $feature = $this->option('feature');

        $this->info('🚀 WIDDX AI Advanced Features Test');
        $this->info('=====================================');

        switch ($feature) {
            case 'all':
                $this->testAllFeatures();
                break;
            case 'search':
                $this->testLiveSearch();
                break;
            case 'image':
                $this->testImageGeneration();
                break;
            case 'voice':
                $this->testVoiceServices();
                break;
            case 'deep-search':
                $this->testDeepSearch();
                break;
            case 'think':
                $this->testThinkMode();
                break;
            case 'document':
                $this->testDocumentAnalysis();
                break;
            case 'vision':
                $this->testVision();
                break;
            default:
                $this->error("Unknown feature: {$feature}");
                $this->info('Available features: all, search, image, voice, deep-search, think, document, vision');
                return 1;
        }

        return 0;
    }

    private function testAllFeatures()
    {
        $this->testLiveSearch();
        $this->testImageGeneration();
        $this->testVoiceServices();
        $this->testDeepSearch();
        $this->testThinkMode();
        $this->testDocumentAnalysis();
        $this->testVision();
    }

    private function testLiveSearch()
    {
        $this->info("\n🔍 Testing Live Search...");

        try {
            // Test free search first
            $freeSearchService = app(FreeSearchService::class);
            $result = $freeSearchService->search('test query', [
                'max_results' => 3,
                'language' => 'en',
            ]);

            if ($result['success']) {
                $this->info("✅ Free Search: Working");
                $this->info("   Results: " . count($result['results']));
                $this->info("   Provider: " . $result['provider']);
            } else {
                $this->warn("⚠️  Free Search: Failed - " . $result['error']);
            }

            // Test paid search if API keys are available
            if (config('services.search.google.api_key')) {
                $paidResult = $this->liveSearch->search('test query', [
                    'max_results' => 3,
                    'language' => 'en',
                ]);

                if ($paidResult['success']) {
                    $this->info("✅ Paid Search: Working");
                    $this->info("   Results: " . count($paidResult['results']));
                    $this->info("   Provider: " . $paidResult['provider']);
                } else {
                    $this->warn("⚠️  Paid Search: Failed - " . $paidResult['error']);
                }
            } else {
                $this->info("ℹ️  Paid Search: Skipped (no API key)");
            }

        } catch (\Exception $e) {
            $this->error("❌ Live Search: Error - " . $e->getMessage());
        }
    }

    private function testImageGeneration()
    {
        $this->info("\n🎨 Testing Image Generation...");

        try {
            // Test free image description
            $freeImageService = app(FreeImageService::class);
            $result = $freeImageService->generateImageDescription('beautiful sunset', [
                'style' => 'realistic',
                'detail' => 'medium',
            ]);

            if ($result['success']) {
                $this->info("✅ Free Image Description: Working");
                $this->info("   Provider: " . $result['provider']);
                $this->info("   Description length: " . strlen($result['description']));
            } else {
                $this->warn("⚠️  Free Image Description: Failed - " . $result['error']);
            }

            // Test paid service if API keys are available
            if (config('services.image_generation.openai.api_key')) {
                $options = $this->imageGeneration->getAvailableOptions();
                $this->info("✅ Paid Image Generation: Service Available");
                $this->info("   Providers: " . implode(', ', $options['providers']));

                $paidResult = $this->imageGeneration->generateImage('test image', [
                    'provider' => 'openai',
                    'size' => '1024x1024',
                ]);

                if ($paidResult['success']) {
                    $this->info("✅ Paid Image Generation: Working");
                } else {
                    $this->warn("⚠️  Paid Image Generation: Failed - " . $paidResult['error']);
                }
            } else {
                $this->info("ℹ️  Paid Image Generation: Skipped (no API key)");
            }

        } catch (\Exception $e) {
            $this->error("❌ Image Generation: Error - " . $e->getMessage());
        }
    }

    private function testVoiceServices()
    {
        $this->info("\n🎤 Testing Voice Services...");

        try {
            // Test free voice services
            $freeVoiceService = app(FreeVoiceService::class);
            $result = $freeVoiceService->generateTtsInstructions('Hello, this is a test', [
                'language' => 'en',
                'voice' => 'default',
            ]);

            if ($result['success']) {
                $this->info("✅ Free Voice Services: Working");
                $this->info("   Provider: " . $result['provider']);
                $this->info("   JavaScript code generated: " . (strlen($result['javascript_code']) > 0 ? 'Yes' : 'No'));
            } else {
                $this->warn("⚠️  Free Voice Services: Failed - " . $result['error']);
            }

            // Test paid services if API keys are available
            if (config('services.voice.openai.api_key')) {
                $voices = $this->voice->getAvailableVoices();
                $this->info("✅ Paid Voice Services: Available");
                $this->info("   TTS Providers: " . implode(', ', array_keys($voices)));

                $paidResult = $this->voice->textToSpeech('Hello, this is a test', [
                    'provider' => 'openai',
                    'language' => 'en',
                ]);

                if ($paidResult['success']) {
                    $this->info("✅ Paid Text-to-Speech: Working");
                } else {
                    $this->warn("⚠️  Paid Text-to-Speech: Failed - " . $paidResult['error']);
                }
            } else {
                $this->info("ℹ️  Paid Voice Services: Skipped (no API key)");
            }

        } catch (\Exception $e) {
            $this->error("❌ Voice Services: Error - " . $e->getMessage());
        }
    }

    private function testDeepSearch()
    {
        $this->info("\n🔬 Testing Deep Search...");

        try {
            // Use a simple query to avoid the array issue
            $result = $this->deepSearch->deepSearch('AI technology', [
                'language' => 'en',
                'region' => 'US',
            ]);

            if ($result['success']) {
                $this->info("✅ Deep Search: Working");
                $this->info("   Sources: " . ($result['sources_analyzed'] ?? 0));
                $this->info("   Insights: " . count($result['insights'] ?? []));
            } else {
                $this->warn("⚠️  Deep Search: Failed - " . ($result['error'] ?? 'Unknown error'));
            }
        } catch (\Exception $e) {
            $this->error("❌ Deep Search: Error - " . $e->getMessage());
            $this->info("   This is expected if search services are not configured");
        }
    }

    private function testThinkMode()
    {
        $this->info("\n🧠 Testing Think Mode...");

        try {
            $result = $this->thinkMode->processWithThinking(
                'What is 2+2?',
                [],
                ['max_tokens' => 500]
            );

            if ($result['success']) {
                $this->info("✅ Think Mode: Working");
                $this->info("   Steps: " . count($result['thinking_process']['steps']));
                $this->info("   Complexity: " . $result['metadata']['complexity_level']);
            } else {
                $this->warn("⚠️  Think Mode: Failed - " . $result['error']);
            }
        } catch (\Exception $e) {
            $this->error("❌ Think Mode: Error - " . $e->getMessage());
        }
    }

    private function testDocumentAnalysis()
    {
        $this->info("\n📄 Testing Document Analysis...");

        try {
            $options = $this->documentAnalysis->getAnalysisOptions();
            $this->info("✅ Document Analysis: Service Available");
            $this->info("   Supported Formats: " . implode(', ', $options['supported_formats']));
            $this->info("   Max File Size: " . ($options['max_file_size'] / 1024 / 1024) . "MB");
        } catch (\Exception $e) {
            $this->error("❌ Document Analysis: Error - " . $e->getMessage());
        }
    }

    private function testVision()
    {
        $this->info("\n👁️  Testing Vision Services...");

        try {
            $capabilities = $this->vision->getCapabilities();
            $this->info("✅ Vision Services: Available");
            $this->info("   Supported Formats: " . implode(', ', $capabilities['supported_formats']));
            $this->info("   Features: " . implode(', ', $capabilities['features']));
            $this->info("   Max File Size: " . ($capabilities['max_file_size'] / 1024 / 1024) . "MB");
        } catch (\Exception $e) {
            $this->error("❌ Vision Services: Error - " . $e->getMessage());
        }
    }
}
