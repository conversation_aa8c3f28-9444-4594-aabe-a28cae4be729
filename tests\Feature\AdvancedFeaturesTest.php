<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class AdvancedFeaturesTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    /** @test */
    public function it_can_get_capabilities()
    {
        $response = $this->getJson('/api/features/capabilities');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'live_search',
                    'image_generation',
                    'voice',
                    'document_analysis',
                    'vision',
                    'deep_search',
                    'think_mode',
                ]);
    }

    /** @test */
    public function it_can_perform_live_search()
    {
        $response = $this->postJson('/api/features/search', [
            'query' => 'test search query',
            'max_results' => 5,
            'language' => 'en',
        ]);

        // Since we don't have real API keys in testing, we expect this to fail gracefully
        $response->assertStatus(500);
        $this->assertArrayHasKey('error', $response->json());
    }

    /** @test */
    public function it_validates_search_request()
    {
        $response = $this->postJson('/api/features/search', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['query']);
    }

    /** @test */
    public function it_can_request_image_generation()
    {
        $response = $this->postJson('/api/features/generate-image', [
            'prompt' => 'a beautiful sunset',
            'size' => '1024x1024',
            'quality' => 'standard',
        ]);

        // Since we don't have real API keys in testing, we expect this to fail gracefully
        $response->assertStatus(500);
        $this->assertArrayHasKey('error', $response->json());
    }

    /** @test */
    public function it_validates_image_generation_request()
    {
        $response = $this->postJson('/api/features/generate-image', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['prompt']);
    }

    /** @test */
    public function it_can_request_text_to_speech()
    {
        $response = $this->postJson('/api/features/text-to-speech', [
            'text' => 'Hello, this is a test message',
            'language' => 'en',
        ]);

        // Since we don't have real API keys in testing, we expect this to fail gracefully
        $response->assertStatus(500);
        $this->assertArrayHasKey('error', $response->json());
    }

    /** @test */
    public function it_validates_text_to_speech_request()
    {
        $response = $this->postJson('/api/features/text-to-speech', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['text']);
    }

    /** @test */
    public function it_can_upload_audio_for_speech_to_text()
    {
        $audioFile = UploadedFile::fake()->create('test.mp3', 1000, 'audio/mpeg');

        $response = $this->postJson('/api/features/speech-to-text', [
            'audio' => $audioFile,
            'language' => 'en',
        ]);

        // Since we don't have real API keys in testing, we expect this to fail gracefully
        $response->assertStatus(500);
        $this->assertArrayHasKey('error', $response->json());
    }

    /** @test */
    public function it_validates_speech_to_text_request()
    {
        $response = $this->postJson('/api/features/speech-to-text', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['audio']);
    }

    /** @test */
    public function it_can_request_deep_search()
    {
        $response = $this->postJson('/api/features/deep-search', [
            'query' => 'artificial intelligence trends',
            'language' => 'en',
        ]);

        // Since we don't have real API keys in testing, we expect this to fail gracefully
        $response->assertStatus(500);
        $this->assertArrayHasKey('error', $response->json());
    }

    /** @test */
    public function it_validates_deep_search_request()
    {
        $response = $this->postJson('/api/features/deep-search', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['query']);
    }

    /** @test */
    public function it_can_request_think_mode()
    {
        $response = $this->postJson('/api/features/think-mode', [
            'message' => 'What is the meaning of life?',
            'conversation_history' => [],
        ]);

        // Since we don't have real API keys in testing, we expect this to fail gracefully
        $response->assertStatus(500);
        $this->assertArrayHasKey('error', $response->json());
    }

    /** @test */
    public function it_validates_think_mode_request()
    {
        $response = $this->postJson('/api/features/think-mode', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['message']);
    }

    /** @test */
    public function it_can_upload_document_for_analysis()
    {
        $document = UploadedFile::fake()->create('test.pdf', 1000, 'application/pdf');

        $response = $this->postJson('/api/features/analyze-document', [
            'document' => $document,
            'analysis_type' => 'comprehensive',
        ]);

        // The document analysis should work even without external APIs for basic text files
        $response->assertStatus(500); // Expected since we're using a fake PDF
        $this->assertArrayHasKey('error', $response->json());
    }

    /** @test */
    public function it_validates_document_analysis_request()
    {
        $response = $this->postJson('/api/features/analyze-document', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['document']);
    }

    /** @test */
    public function it_rejects_invalid_document_types()
    {
        $invalidFile = UploadedFile::fake()->create('test.exe', 1000, 'application/x-executable');

        $response = $this->postJson('/api/features/analyze-document', [
            'document' => $invalidFile,
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['document']);
    }

    /** @test */
    public function it_can_upload_image_for_analysis()
    {
        $image = UploadedFile::fake()->image('test.jpg', 800, 600);

        $response = $this->postJson('/api/features/analyze-image', [
            'image' => $image,
            'extract_text' => true,
            'detect_objects' => true,
        ]);

        // Since we don't have real API keys in testing, we expect this to fail gracefully
        $response->assertStatus(500);
        $this->assertArrayHasKey('error', $response->json());
    }

    /** @test */
    public function it_validates_image_analysis_request()
    {
        $response = $this->postJson('/api/features/analyze-image', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['image']);
    }

    /** @test */
    public function it_rejects_invalid_image_types()
    {
        $invalidFile = UploadedFile::fake()->create('test.txt', 1000, 'text/plain');

        $response = $this->postJson('/api/features/analyze-image', [
            'image' => $invalidFile,
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['image']);
    }

    /** @test */
    public function it_enforces_file_size_limits()
    {
        // Test document size limit (10MB)
        $largeDocument = UploadedFile::fake()->create('large.pdf', 11000, 'application/pdf'); // 11MB

        $response = $this->postJson('/api/features/analyze-document', [
            'document' => $largeDocument,
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['document']);

        // Test image size limit (20MB)
        $largeImage = UploadedFile::fake()->create('large.jpg', 21000, 'image/jpeg'); // 21MB

        $response = $this->postJson('/api/features/analyze-image', [
            'image' => $largeImage,
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['image']);
    }

    /** @test */
    public function it_handles_rate_limiting()
    {
        // This test would need to be implemented based on your rate limiting configuration
        $this->markTestSkipped('Rate limiting test needs specific configuration');
    }

    /** @test */
    public function it_handles_csrf_protection()
    {
        $response = $this->post('/api/features/search', [
            'query' => 'test',
        ]);

        // Should fail without CSRF token in a real scenario
        // In testing, this might behave differently based on your test setup
        $this->assertTrue(true); // Placeholder assertion
    }
}
