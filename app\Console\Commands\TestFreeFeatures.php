<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\FreeSearchService;
use App\Services\FreeImageService;
use App\Services\FreeVoiceService;
use App\Services\ThinkModeService;

class TestFreeFeatures extends Command
{
    protected $signature = 'widdx:test-free {--feature=all : Specific feature to test (all, search, image, voice, think)}';
    protected $description = 'Test WIDDX AI free features only';

    private FreeSearchService $freeSearch;
    private FreeImageService $freeImage;
    private FreeVoiceService $freeVoice;
    private ThinkModeService $thinkMode;

    public function __construct(
        FreeSearchService $freeSearch,
        FreeImageService $freeImage,
        FreeVoiceService $freeVoice,
        ThinkModeService $thinkMode
    ) {
        parent::__construct();
        
        $this->freeSearch = $freeSearch;
        $this->freeImage = $freeImage;
        $this->freeVoice = $freeVoice;
        $this->thinkMode = $thinkMode;
    }

    public function handle()
    {
        $feature = $this->option('feature');

        $this->info('🆓 WIDDX AI Free Features Test');
        $this->info('===============================');
        $this->info('Testing only free alternatives that work without paid API keys');

        switch ($feature) {
            case 'all':
                $this->testAllFreeFeatures();
                break;
            case 'search':
                $this->testFreeSearch();
                break;
            case 'image':
                $this->testFreeImage();
                break;
            case 'voice':
                $this->testFreeVoice();
                break;
            case 'think':
                $this->testThinkMode();
                break;
            default:
                $this->error("Unknown feature: {$feature}");
                $this->info('Available features: all, search, image, voice, think');
                return 1;
        }

        $this->info("\n🎉 Free features testing completed!");
        $this->info("💡 These features work without any paid API keys");
        $this->info("💰 Total cost: Only DeepSeek (~$2/month) + Gemini (free tier)");

        return 0;
    }

    private function testAllFreeFeatures()
    {
        $this->testFreeSearch();
        $this->testFreeImage();
        $this->testFreeVoice();
        $this->testThinkMode();
    }

    private function testFreeSearch()
    {
        $this->info("\n🔍 Testing Free Search (DuckDuckGo)...");
        
        try {
            // Test basic search
            $result = $this->freeSearch->search('artificial intelligence', [
                'max_results' => 3,
                'language' => 'en',
            ]);

            if ($result['success']) {
                $this->info("✅ Free Search: Working");
                $this->info("   Provider: " . $result['provider']);
                $this->info("   Results: " . count($result['results']));
                
                if (!empty($result['results'])) {
                    $this->info("   Sample result: " . $result['results'][0]['title']);
                }
            } else {
                $this->warn("⚠️  Free Search: Failed - " . $result['error']);
            }

            // Test search suggestions
            $suggestions = $this->freeSearch->getSuggestions('AI');
            if ($suggestions['success']) {
                $this->info("✅ Search Suggestions: Working");
                $this->info("   Suggestions: " . count($suggestions['suggestions']));
            }

            // Test trends
            $trends = $this->freeSearch->getTrends('SA', 'ar');
            if ($trends['success']) {
                $this->info("✅ Trending Topics: Working");
                $this->info("   Trends: " . count($trends['trends']));
            }

        } catch (\Exception $e) {
            $this->error("❌ Free Search: Error - " . $e->getMessage());
        }
    }

    private function testFreeImage()
    {
        $this->info("\n🎨 Testing Free Image Services (Gemini-powered)...");
        
        try {
            // Test image description
            $result = $this->freeImage->generateImageDescription('beautiful sunset over mountains', [
                'style' => 'realistic',
                'detail' => 'high',
            ]);

            if ($result['success']) {
                $this->info("✅ Image Description: Working");
                $this->info("   Provider: " . $result['provider']);
                $this->info("   Description length: " . strlen($result['description']) . " characters");
                $this->info("   ASCII art: " . (!empty($result['ascii_art']) ? 'Generated' : 'None'));
            } else {
                $this->warn("⚠️  Image Description: Failed - " . $result['error']);
            }

            // Test SVG generation
            $svgResult = $this->freeImage->generateSvgImage('simple house', [
                'style' => 'simple',
            ]);

            if ($svgResult['success']) {
                $this->info("✅ SVG Generation: Working");
                $this->info("   Provider: " . $svgResult['provider']);
                $this->info("   SVG file: " . $svgResult['image_path']);
            } else {
                $this->warn("⚠️  SVG Generation: Failed - " . $svgResult['error']);
            }

            // Test placeholder
            $placeholderResult = $this->freeImage->generatePlaceholderImage('Test Image', [
                'width' => 400,
                'height' => 300,
            ]);

            if ($placeholderResult['success']) {
                $this->info("✅ Placeholder Images: Working");
                $this->info("   File: " . $placeholderResult['image_path']);
            }

        } catch (\Exception $e) {
            $this->error("❌ Free Image Services: Error - " . $e->getMessage());
        }
    }

    private function testFreeVoice()
    {
        $this->info("\n🎤 Testing Free Voice Services (Browser-based)...");
        
        try {
            // Test TTS instructions
            $ttsResult = $this->freeVoice->generateTtsInstructions('مرحبا، هذا اختبار للصوت', [
                'language' => 'ar',
                'voice' => 'default',
                'speed' => 1.0,
            ]);

            if ($ttsResult['success']) {
                $this->info("✅ Text-to-Speech Instructions: Working");
                $this->info("   Provider: " . $ttsResult['provider']);
                $this->info("   JavaScript code: " . (strlen($ttsResult['javascript_code']) > 100 ? 'Generated' : 'Failed'));
            } else {
                $this->warn("⚠️  TTS Instructions: Failed - " . $ttsResult['error']);
            }

            // Test STT instructions
            $sttResult = $this->freeVoice->generateSttInstructions([
                'language' => 'ar',
                'continuous' => false,
            ]);

            if ($sttResult['success']) {
                $this->info("✅ Speech-to-Text Instructions: Working");
                $this->info("   Provider: " . $sttResult['provider']);
                $this->info("   JavaScript code: " . (strlen($sttResult['javascript_code']) > 100 ? 'Generated' : 'Failed'));
            } else {
                $this->warn("⚠️  STT Instructions: Failed - " . $sttResult['error']);
            }

            // Test voice widget
            $widgetResult = $this->freeVoice->generateVoiceWidget([
                'language' => 'ar',
                'theme' => 'dark',
            ]);

            if ($widgetResult['success']) {
                $this->info("✅ Voice Widget: Working");
                $this->info("   HTML/CSS/JS: Generated");
            }

        } catch (\Exception $e) {
            $this->error("❌ Free Voice Services: Error - " . $e->getMessage());
        }
    }

    private function testThinkMode()
    {
        $this->info("\n🧠 Testing Think Mode (DeepSeek-powered)...");
        
        try {
            $result = $this->thinkMode->processWithThinking(
                'What is 2+2 and why?',
                [],
                ['max_tokens' => 300]
            );

            if ($result['success']) {
                $this->info("✅ Think Mode: Working");
                $this->info("   Steps: " . count($result['thinking_process']['steps']));
                $this->info("   Complexity: " . $result['metadata']['complexity_level']);
                $this->info("   Thinking time: " . round($result['metadata']['thinking_time'], 2) . "s");
            } else {
                $this->warn("⚠️  Think Mode: Failed - " . $result['error']);
            }
        } catch (\Exception $e) {
            $this->error("❌ Think Mode: Error - " . $e->getMessage());
            $this->info("   Make sure DeepSeek API key is configured");
        }
    }
}
