<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class LiveSearchService
{
    private array $searchProviders;
    private int $timeout;
    private int $cacheMinutes;

    public function __construct()
    {
        $this->searchProviders = [
            'google' => config('services.search.google'),
            'bing' => config('services.search.bing'),
            'serp' => config('services.search.serp'),
        ];
        $this->timeout = 30;
        $this->cacheMinutes = 15; // Cache results for 15 minutes
    }

    /**
     * Perform live search across multiple providers
     */
    public function search(string $query, array $options = []): array
    {
        try {
            $cacheKey = 'live_search_' . md5($query . serialize($options));
            
            // Check cache first
            if ($cached = Cache::get($cacheKey)) {
                Log::info('Live search cache hit', ['query' => $query]);
                return $cached;
            }

            $provider = $options['provider'] ?? 'google';
            $maxResults = $options['max_results'] ?? 10;
            $language = $options['language'] ?? 'ar';
            $region = $options['region'] ?? 'SA';

            $results = match($provider) {
                'google' => $this->searchGoogle($query, $maxResults, $language, $region),
                'bing' => $this->searchBing($query, $maxResults, $language, $region),
                'serp' => $this->searchSerp($query, $maxResults, $language, $region),
                default => $this->searchGoogle($query, $maxResults, $language, $region),
            };

            // Cache the results
            Cache::put($cacheKey, $results, now()->addMinutes($this->cacheMinutes));

            Log::info('Live search completed', [
                'query' => $query,
                'provider' => $provider,
                'results_count' => count($results['results'] ?? []),
            ]);

            return $results;

        } catch (\Exception $e) {
            Log::error('Live search error', [
                'query' => $query,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'results' => [],
                'query' => $query,
            ];
        }
    }

    /**
     * Search using Google Custom Search API
     */
    private function searchGoogle(string $query, int $maxResults, string $language, string $region): array
    {
        $config = $this->searchProviders['google'];
        
        if (!$config['api_key'] || !$config['search_engine_id']) {
            throw new \Exception('Google Search API credentials not configured');
        }

        $response = Http::timeout($this->timeout)
            ->get($config['base_url'], [
                'key' => $config['api_key'],
                'cx' => $config['search_engine_id'],
                'q' => $query,
                'num' => min($maxResults, 10),
                'lr' => "lang_{$language}",
                'gl' => $region,
                'safe' => 'active',
            ]);

        if (!$response->successful()) {
            throw new \Exception('Google Search API request failed: ' . $response->body());
        }

        $data = $response->json();
        
        return [
            'success' => true,
            'provider' => 'google',
            'query' => $query,
            'total_results' => $data['searchInformation']['totalResults'] ?? 0,
            'search_time' => $data['searchInformation']['searchTime'] ?? 0,
            'results' => $this->formatGoogleResults($data['items'] ?? []),
        ];
    }

    /**
     * Search using Bing Search API
     */
    private function searchBing(string $query, int $maxResults, string $language, string $region): array
    {
        $config = $this->searchProviders['bing'];
        
        if (!$config['api_key']) {
            throw new \Exception('Bing Search API credentials not configured');
        }

        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'Ocp-Apim-Subscription-Key' => $config['api_key'],
            ])
            ->get($config['base_url'], [
                'q' => $query,
                'count' => min($maxResults, 50),
                'mkt' => $language . '-' . $region,
                'safeSearch' => 'Moderate',
            ]);

        if (!$response->successful()) {
            throw new \Exception('Bing Search API request failed: ' . $response->body());
        }

        $data = $response->json();
        
        return [
            'success' => true,
            'provider' => 'bing',
            'query' => $query,
            'total_results' => $data['webPages']['totalEstimatedMatches'] ?? 0,
            'results' => $this->formatBingResults($data['webPages']['value'] ?? []),
        ];
    }

    /**
     * Search using SerpApi
     */
    private function searchSerp(string $query, int $maxResults, string $language, string $region): array
    {
        $config = $this->searchProviders['serp'];
        
        if (!$config['api_key']) {
            throw new \Exception('SerpApi credentials not configured');
        }

        $response = Http::timeout($this->timeout)
            ->get($config['base_url'], [
                'api_key' => $config['api_key'],
                'engine' => 'google',
                'q' => $query,
                'num' => min($maxResults, 100),
                'hl' => $language,
                'gl' => strtolower($region),
                'safe' => 'active',
            ]);

        if (!$response->successful()) {
            throw new \Exception('SerpApi request failed: ' . $response->body());
        }

        $data = $response->json();
        
        return [
            'success' => true,
            'provider' => 'serp',
            'query' => $query,
            'total_results' => $data['search_information']['total_results'] ?? 0,
            'search_time' => $data['search_information']['time_taken_displayed'] ?? 0,
            'results' => $this->formatSerpResults($data['organic_results'] ?? []),
        ];
    }

    /**
     * Format Google search results
     */
    private function formatGoogleResults(array $items): array
    {
        return array_map(function ($item) {
            return [
                'title' => $item['title'] ?? '',
                'url' => $item['link'] ?? '',
                'snippet' => $item['snippet'] ?? '',
                'display_url' => $item['displayLink'] ?? '',
                'formatted_url' => $item['formattedUrl'] ?? '',
            ];
        }, $items);
    }

    /**
     * Format Bing search results
     */
    private function formatBingResults(array $items): array
    {
        return array_map(function ($item) {
            return [
                'title' => $item['name'] ?? '',
                'url' => $item['url'] ?? '',
                'snippet' => $item['snippet'] ?? '',
                'display_url' => $item['displayUrl'] ?? '',
                'formatted_url' => $item['url'] ?? '',
            ];
        }, $items);
    }

    /**
     * Format SerpApi search results
     */
    private function formatSerpResults(array $items): array
    {
        return array_map(function ($item) {
            return [
                'title' => $item['title'] ?? '',
                'url' => $item['link'] ?? '',
                'snippet' => $item['snippet'] ?? '',
                'display_url' => $item['displayed_link'] ?? '',
                'formatted_url' => $item['link'] ?? '',
            ];
        }, $items);
    }

    /**
     * Get trending topics and news
     */
    public function getTrends(string $region = 'SA', string $language = 'ar'): array
    {
        try {
            $cacheKey = "trends_{$region}_{$language}";
            
            if ($cached = Cache::get($cacheKey)) {
                return $cached;
            }

            // Search for trending topics
            $trendingQueries = [
                'الأخبار العاجلة',
                'آخر الأخبار',
                'trending now',
                'breaking news',
            ];

            $trends = [];
            foreach ($trendingQueries as $query) {
                $results = $this->search($query, [
                    'max_results' => 5,
                    'language' => $language,
                    'region' => $region,
                ]);
                
                if ($results['success']) {
                    $trends = array_merge($trends, $results['results']);
                }
            }

            $formattedTrends = [
                'success' => true,
                'region' => $region,
                'language' => $language,
                'trends' => array_slice($trends, 0, 20), // Limit to 20 trends
                'updated_at' => now()->toISOString(),
            ];

            Cache::put($cacheKey, $formattedTrends, now()->addMinutes(30));
            
            return $formattedTrends;

        } catch (\Exception $e) {
            Log::error('Trends fetch error', [
                'error' => $e->getMessage(),
                'region' => $region,
                'language' => $language,
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'trends' => [],
            ];
        }
    }
}
