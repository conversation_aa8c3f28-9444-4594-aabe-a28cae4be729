<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>WIDDX AI - Intelligent Assistant</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'widdx-primary': '#6366f1',
                        'widdx-secondary': '#8b5cf6',
                        'widdx-dark': '#0f172a',
                        'widdx-darker': '#020617',
                    }
                }
            }
        }
    </script>
    <style>
        .typing-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #6366f1;
            animation: typing 1.4s infinite ease-in-out;
        }
        .typing-indicator:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .message-fade-in {
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .feature-btn {
            @apply flex flex-col items-center justify-center p-3 rounded-lg text-white transition-all duration-200 hover:scale-105 active:scale-95;
            min-height: 60px;
        }

        .feature-btn.active {
            @apply ring-2 ring-white ring-opacity-50;
        }
    </style>
</head>
<body class="bg-widdx-darker text-white min-h-screen">
    <div id="app" class="flex flex-col h-screen">
        <!-- Header -->
        <header class="bg-widdx-dark border-b border-gray-700 p-4">
            <div class="max-w-4xl mx-auto flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-widdx-primary to-widdx-secondary rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-sm">W</span>
                    </div>
                    <h1 class="text-xl font-bold">WIDDX AI</h1>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Personality Selector -->
                    <select id="personality-selector" class="bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600 focus:border-widdx-primary focus:outline-none">
                        <?php $__currentLoopData = $personalities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $personality): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($personality['name']); ?>" <?php echo e($personality['name'] === 'neutral' ? 'selected' : ''); ?>>
                                <?php echo e($personality['display_name']); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>

                    <!-- Advanced Features Toggle -->
                    <button id="features-toggle" class="bg-widdx-primary hover:bg-widdx-secondary text-white px-3 py-2 rounded-lg text-sm transition-colors">
                        🚀 Features
                    </button>

                    <!-- Clear Chat Button -->
                    <button id="clear-chat" class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg text-sm transition-colors">
                        Clear Chat
                    </button>
                </div>
            </div>
        </header>

        <!-- Advanced Features Panel -->
        <div id="features-panel" class="hidden bg-gray-800 border-b border-gray-700 p-4">
            <div class="max-w-4xl mx-auto">
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-3">
                    <!-- Live Search -->
                    <button class="feature-btn bg-blue-600 hover:bg-blue-700" data-feature="search">
                        <span class="text-lg">🔍</span>
                        <span class="text-xs">Search</span>
                    </button>

                    <!-- Deep Search -->
                    <button class="feature-btn bg-purple-600 hover:bg-purple-700" data-feature="deep-search">
                        <span class="text-lg">🔬</span>
                        <span class="text-xs">Deep Search</span>
                    </button>

                    <!-- Image Generation -->
                    <button class="feature-btn bg-green-600 hover:bg-green-700" data-feature="image-gen">
                        <span class="text-lg">🎨</span>
                        <span class="text-xs">Generate Image</span>
                    </button>

                    <!-- Think Mode -->
                    <button class="feature-btn bg-yellow-600 hover:bg-yellow-700" data-feature="think-mode">
                        <span class="text-lg">🧠</span>
                        <span class="text-xs">Think Mode</span>
                    </button>

                    <!-- Voice -->
                    <button class="feature-btn bg-red-600 hover:bg-red-700" data-feature="voice">
                        <span class="text-lg">🎤</span>
                        <span class="text-xs">Voice</span>
                    </button>

                    <!-- Document Analysis -->
                    <button class="feature-btn bg-indigo-600 hover:bg-indigo-700" data-feature="document">
                        <span class="text-lg">📄</span>
                        <span class="text-xs">Document</span>
                    </button>

                    <!-- Image Analysis -->
                    <button class="feature-btn bg-pink-600 hover:bg-pink-700" data-feature="vision">
                        <span class="text-lg">👁️</span>
                        <span class="text-xs">Vision</span>
                    </button>

                    <!-- Trends -->
                    <button class="feature-btn bg-teal-600 hover:bg-teal-700" data-feature="trends">
                        <span class="text-lg">📈</span>
                        <span class="text-xs">Trends</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Messages -->
        <main class="flex-1 overflow-y-auto p-4">
            <div class="max-w-4xl mx-auto">
                <div id="messages-container" class="space-y-4">
                    <!-- Welcome Message -->
                    <div class="flex items-start space-x-3 message-fade-in">
                        <div class="w-8 h-8 bg-gradient-to-r from-widdx-primary to-widdx-secondary rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-white font-bold text-sm">W</span>
                        </div>
                        <div class="bg-gray-800 rounded-lg p-4 max-w-3xl">
                            <p class="text-gray-100">
                                Hello! I'm WIDDX, your intelligent AI assistant. I'm here to help you with questions, tasks, and conversations.
                                You can adjust my personality using the selector above. How can I assist you today?
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Typing Indicator -->
                <div id="typing-indicator" class="hidden flex items-start space-x-3 mt-4">
                    <div class="w-8 h-8 bg-gradient-to-r from-widdx-primary to-widdx-secondary rounded-full flex items-center justify-center flex-shrink-0">
                        <span class="text-white font-bold text-sm">W</span>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-4">
                        <div class="flex space-x-1">
                            <div class="typing-indicator"></div>
                            <div class="typing-indicator"></div>
                            <div class="typing-indicator"></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Input Area -->
        <footer class="bg-widdx-dark border-t border-gray-700 p-4">
            <div class="max-w-4xl mx-auto">
                <form id="chat-form" class="flex space-x-4">
                    <div class="flex-1">
                        <textarea
                            id="message-input"
                            placeholder="Type your message to WIDDX..."
                            class="w-full bg-gray-800 text-white rounded-lg px-4 py-3 border border-gray-600 focus:border-widdx-primary focus:outline-none resize-none"
                            rows="1"
                            maxlength="10000"
                        ></textarea>
                    </div>
                    <button
                        type="submit"
                        id="send-button"
                        class="bg-widdx-primary hover:bg-widdx-secondary text-white px-6 py-3 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        Send
                    </button>
                </form>

                <div class="mt-2 text-xs text-gray-400 text-center">
                    WIDDX AI - Your Intelligent Assistant
                </div>
            </div>
        </footer>
    </div>

    <script>
        class WiddxChat {
            constructor() {
                this.sessionId = this.generateSessionId();
                this.currentPersonality = 'neutral';
                this.isTyping = false;
                this.activeFeature = null;
                this.featuresVisible = false;

                this.initializeElements();
                this.bindEvents();
                this.autoResizeTextarea();
            }

            initializeElements() {
                this.messagesContainer = document.getElementById('messages-container');
                this.messageInput = document.getElementById('message-input');
                this.sendButton = document.getElementById('send-button');
                this.chatForm = document.getElementById('chat-form');
                this.personalitySelector = document.getElementById('personality-selector');
                this.clearChatButton = document.getElementById('clear-chat');
                this.typingIndicator = document.getElementById('typing-indicator');
                this.featuresToggle = document.getElementById('features-toggle');
                this.featuresPanel = document.getElementById('features-panel');
                this.featureButtons = document.querySelectorAll('.feature-btn');
            }

            bindEvents() {
                this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
                this.personalitySelector.addEventListener('change', (e) => this.handlePersonalityChange(e));
                this.clearChatButton.addEventListener('click', () => this.clearChat());
                this.messageInput.addEventListener('keydown', (e) => this.handleKeydown(e));
                this.featuresToggle.addEventListener('click', () => this.toggleFeatures());

                // Bind feature buttons
                this.featureButtons.forEach(btn => {
                    btn.addEventListener('click', (e) => this.handleFeatureClick(e));
                });
            }

            generateSessionId() {
                return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }

            autoResizeTextarea() {
                this.messageInput.addEventListener('input', () => {
                    this.messageInput.style.height = 'auto';
                    this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
                });
            }

            handleKeydown(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.chatForm.dispatchEvent(new Event('submit'));
                }
            }

            async handleSubmit(e) {
                e.preventDefault();

                const message = this.messageInput.value.trim();
                if (!message || this.isTyping) return;

                this.addUserMessage(message);
                this.messageInput.value = '';
                this.messageInput.style.height = 'auto';
                this.setTyping(true);

                try {
                    const response = await this.sendMessage(message);
                    this.addWiddxMessage(response.message);
                } catch (error) {
                    this.addWiddxMessage('I apologize, but I encountered an issue processing your request. Please try again.');
                    console.error('Chat error:', error);
                } finally {
                    this.setTyping(false);
                }
            }

            async sendMessage(message) {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: this.sessionId,
                        personality: this.currentPersonality
                    })
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                return await response.json();
            }

            addUserMessage(message) {
                const messageElement = this.createMessageElement('user', message);
                this.messagesContainer.appendChild(messageElement);
                this.scrollToBottom();
            }

            addWiddxMessage(message) {
                const messageElement = this.createMessageElement('widdx', message);
                this.messagesContainer.appendChild(messageElement);
                this.scrollToBottom();
            }

            createMessageElement(role, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'flex items-start space-x-3 message-fade-in';

                if (role === 'user') {
                    messageDiv.className += ' flex-row-reverse space-x-reverse';
                    messageDiv.innerHTML = `
                        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-white font-bold text-sm">U</span>
                        </div>
                        <div class="bg-widdx-primary rounded-lg p-4 max-w-3xl">
                            <p class="text-white">${this.escapeHtml(content)}</p>
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="w-8 h-8 bg-gradient-to-r from-widdx-primary to-widdx-secondary rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-white font-bold text-sm">W</span>
                        </div>
                        <div class="bg-gray-800 rounded-lg p-4 max-w-3xl">
                            <p class="text-gray-100">${this.escapeHtml(content)}</p>
                        </div>
                    `;
                }

                return messageDiv;
            }

            setTyping(isTyping) {
                this.isTyping = isTyping;
                this.sendButton.disabled = isTyping;
                this.messageInput.disabled = isTyping;

                if (isTyping) {
                    this.typingIndicator.classList.remove('hidden');
                    this.scrollToBottom();
                } else {
                    this.typingIndicator.classList.add('hidden');
                }
            }

            handlePersonalityChange(e) {
                this.currentPersonality = e.target.value;
                // Optionally show a message about personality change
                console.log('Personality changed to:', this.currentPersonality);
            }

            clearChat() {
                // Remove all messages except the welcome message
                const messages = this.messagesContainer.children;
                for (let i = messages.length - 1; i > 0; i--) {
                    messages[i].remove();
                }

                // Generate new session ID
                this.sessionId = this.generateSessionId();
            }

            scrollToBottom() {
                setTimeout(() => {
                    window.scrollTo({
                        top: document.body.scrollHeight,
                        behavior: 'smooth'
                    });
                }, 100);
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            toggleFeatures() {
                this.featuresVisible = !this.featuresVisible;
                if (this.featuresVisible) {
                    this.featuresPanel.classList.remove('hidden');
                    this.featuresToggle.textContent = '🚀 Hide Features';
                } else {
                    this.featuresPanel.classList.add('hidden');
                    this.featuresToggle.textContent = '🚀 Features';
                    this.clearActiveFeature();
                }
            }

            handleFeatureClick(e) {
                const feature = e.currentTarget.dataset.feature;
                this.setActiveFeature(feature);

                switch(feature) {
                    case 'search':
                        this.messageInput.placeholder = 'ابحث عن: اكتب استفسارك هنا... (بحث مجاني)';
                        this.messageInput.value = 'ابحث عن ';
                        break;
                    case 'deep-search':
                        this.messageInput.placeholder = 'بحث عميق: اكتب موضوعك للبحث المتعمق... (مجاني)';
                        this.messageInput.value = 'بحث عميق ';
                        break;
                    case 'image-gen':
                        this.messageInput.placeholder = 'وصف الصورة: اكتب وصفاً للصورة... (وصف مجاني)';
                        this.messageInput.value = 'ارسم ';
                        break;
                    case 'think-mode':
                        this.messageInput.placeholder = 'وضع التفكير: اطرح سؤالاً معقداً...';
                        this.addMessage('system', '🧠 تم تفعيل وضع التفكير - سأعرض لك عملية تفكيري خطوة بخطوة');
                        break;
                    case 'voice':
                        this.handleVoiceFeature();
                        break;
                    case 'document':
                        this.handleDocumentUpload();
                        break;
                    case 'vision':
                        this.handleImageUpload();
                        break;
                    case 'trends':
                        this.getTrends();
                        break;
                }

                this.messageInput.focus();
            }

            setActiveFeature(feature) {
                // Remove active class from all buttons
                this.featureButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to selected button
                const activeBtn = document.querySelector(`[data-feature="${feature}"]`);
                if (activeBtn) {
                    activeBtn.classList.add('active');
                }

                this.activeFeature = feature;
            }

            clearActiveFeature() {
                this.featureButtons.forEach(btn => btn.classList.remove('active'));
                this.activeFeature = null;
                this.messageInput.placeholder = 'Type your message here...';
            }

            async handleVoiceFeature() {
                try {
                    const response = await fetch('/api/features/voice-widget', {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    });

                    const result = await response.json();

                    if (result.success) {
                        // Add voice widget to the page
                        this.addVoiceWidget(result);
                        this.addMessage('system', '🎤 تم تفعيل الميزات الصوتية المجانية! يمكنك الآن استخدام المتصفح للتحدث والاستماع.');
                    } else {
                        this.addMessage('system', '🎤 الميزات الصوتية متاحة عبر المتصفح. استخدم أزرار التحكم الصوتي أدناه.');
                        this.addBasicVoiceControls();
                    }
                } catch (error) {
                    this.addMessage('system', '🎤 الميزات الصوتية متاحة عبر المتصفح. استخدم أزرار التحكم الصوتي أدناه.');
                    this.addBasicVoiceControls();
                }
            }

            addVoiceWidget(widgetData) {
                // Create voice widget container
                const widgetContainer = document.createElement('div');
                widgetContainer.innerHTML = widgetData.html;

                // Add CSS
                const style = document.createElement('style');
                style.textContent = widgetData.css;
                document.head.appendChild(style);

                // Add to messages container
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message widdx-message';
                messageDiv.innerHTML = `
                    <div class="message-content">
                        <div class="message-text">
                            🎤 <strong>عنصر التحكم الصوتي المجاني</strong><br>
                            ${widgetContainer.innerHTML}
                        </div>
                        <div class="message-time">${new Date().toLocaleTimeString()}</div>
                    </div>
                `;

                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();

                // Execute JavaScript
                const script = document.createElement('script');
                script.textContent = widgetData.javascript;
                document.body.appendChild(script);
            }

            addBasicVoiceControls() {
                const voiceControlsHtml = `
                    <div class="voice-controls-basic" style="background: #1f2937; border-radius: 8px; padding: 16px; margin: 8px 0;">
                        <div style="display: flex; gap: 12px; margin-bottom: 12px;">
                            <button onclick="startBasicRecording()" style="background: #3b82f6; color: white; border: none; border-radius: 6px; padding: 8px 16px; cursor: pointer;">
                                🎤 ابدأ التسجيل
                            </button>
                            <button onclick="speakLastMessage()" style="background: #10b981; color: white; border: none; border-radius: 6px; padding: 8px 16px; cursor: pointer;">
                                🔊 اقرأ آخر رسالة
                            </button>
                        </div>
                        <div id="voice-status-basic" style="font-size: 12px; color: #9ca3af;"></div>
                    </div>
                `;

                const messageDiv = document.createElement('div');
                messageDiv.className = 'message widdx-message';
                messageDiv.innerHTML = `
                    <div class="message-content">
                        <div class="message-text">
                            ${voiceControlsHtml}
                        </div>
                        <div class="message-time">${new Date().toLocaleTimeString()}</div>
                    </div>
                `;

                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();

                // Add basic voice functions
                this.addBasicVoiceFunctions();
            }

            addBasicVoiceFunctions() {
                if (window.startBasicRecording) return; // Already added

                window.startBasicRecording = () => {
                    const status = document.getElementById('voice-status-basic');

                    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                        const recognition = new SpeechRecognition();

                        recognition.lang = 'ar-SA';
                        recognition.continuous = false;
                        recognition.interimResults = false;

                        recognition.onstart = () => {
                            status.textContent = 'جاري الاستماع... تحدث الآن';
                        };

                        recognition.onresult = (event) => {
                            const transcript = event.results[0][0].transcript;
                            this.messageInput.value = transcript;
                            status.textContent = 'تم التعرف على: ' + transcript;
                        };

                        recognition.onerror = (event) => {
                            status.textContent = 'خطأ في التعرف على الكلام: ' + event.error;
                        };

                        recognition.onend = () => {
                            status.textContent = 'انتهى التسجيل';
                        };

                        recognition.start();
                    } else {
                        status.textContent = 'متصفحك لا يدعم التعرف على الكلام';
                    }
                };

                window.speakLastMessage = () => {
                    const status = document.getElementById('voice-status-basic');
                    const messages = document.querySelectorAll('.widdx-message .message-text');

                    if (messages.length > 0) {
                        const lastMessage = messages[messages.length - 1].textContent;

                        if ('speechSynthesis' in window) {
                            speechSynthesis.cancel();

                            const utterance = new SpeechSynthesisUtterance(lastMessage);
                            utterance.lang = 'ar-SA';
                            utterance.rate = 1.0;

                            utterance.onstart = () => {
                                status.textContent = 'جاري القراءة...';
                            };

                            utterance.onend = () => {
                                status.textContent = 'انتهت القراءة';
                            };

                            speechSynthesis.speak(utterance);
                        } else {
                            status.textContent = 'متصفحك لا يدعم تحويل النص إلى كلام';
                        }
                    } else {
                        status.textContent = 'لا توجد رسائل للقراءة';
                    }
                };
            }

            async handleDocumentUpload() {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.pdf,.docx,.doc,.txt,.rtf,.xlsx,.xls,.csv';
                input.onchange = async (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        await this.uploadAndAnalyzeDocument(file);
                    }
                };
                input.click();
            }

            async handleImageUpload() {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = 'image/*';
                input.onchange = async (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        await this.uploadAndAnalyzeImage(file);
                    }
                };
                input.click();
            }

            async uploadAndAnalyzeDocument(file) {
                const formData = new FormData();
                formData.append('document', file);

                this.addMessage('user', `📄 تحليل المستند: ${file.name}`);
                this.showTyping();

                try {
                    const response = await fetch('/api/features/analyze-document', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: formData
                    });

                    const result = await response.json();
                    this.hideTyping();

                    if (result.success) {
                        let message = `📄 تحليل المستند: ${result.filename}\n\n`;
                        message += `📊 **الملخص:**\n${result.summary}\n\n`;

                        if (result.key_information && result.key_information.facts) {
                            message += `🔍 **الحقائق الرئيسية:**\n`;
                            result.key_information.facts.forEach(fact => {
                                message += `• ${fact}\n`;
                            });
                        }

                        this.addMessage('widdx', message);
                    } else {
                        this.addMessage('widdx', `❌ فشل في تحليل المستند: ${result.error}`);
                    }
                } catch (error) {
                    this.hideTyping();
                    this.addMessage('widdx', '❌ حدث خطأ أثناء تحليل المستند');
                }
            }

            async uploadAndAnalyzeImage(file) {
                const formData = new FormData();
                formData.append('image', file);
                formData.append('extract_text', 'true');
                formData.append('detect_objects', 'true');

                this.addMessage('user', `🖼️ تحليل الصورة: ${file.name}`);
                this.showTyping();

                try {
                    const response = await fetch('/api/features/analyze-image', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: formData
                    });

                    const result = await response.json();
                    this.hideTyping();

                    if (result.success) {
                        let message = `🖼️ تحليل الصورة: ${result.filename}\n\n`;
                        message += `👁️ **الوصف:**\n${result.analysis.description}\n\n`;

                        if (result.extracted_text && result.extracted_text !== 'لا يوجد نص مرئي في الصورة') {
                            message += `📝 **النص المستخرج:**\n${result.extracted_text}\n\n`;
                        }

                        if (result.detected_objects && result.detected_objects.length > 0) {
                            message += `🔍 **الكائنات المكتشفة:**\n`;
                            result.detected_objects.forEach(obj => {
                                message += `• ${obj.name}\n`;
                            });
                        }

                        this.addMessage('widdx', message);
                    } else {
                        this.addMessage('widdx', `❌ فشل في تحليل الصورة: ${result.error}`);
                    }
                } catch (error) {
                    this.hideTyping();
                    this.addMessage('widdx', '❌ حدث خطأ أثناء تحليل الصورة');
                }
            }

            async getTrends() {
                this.addMessage('user', '📈 عرض الاتجاهات الحالية');
                this.showTyping();

                try {
                    const response = await fetch('/api/features/search', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            query: 'trending topics اتجاهات حالية',
                            max_results: 5
                        })
                    });

                    const result = await response.json();
                    this.hideTyping();

                    if (result.success) {
                        let message = '📈 **الاتجاهات الحالية:**\n\n';
                        result.results.forEach((item, index) => {
                            message += `${index + 1}. **${item.title}**\n`;
                            message += `   ${item.snippet}\n`;
                            message += `   🔗 ${item.url}\n\n`;
                        });

                        this.addMessage('widdx', message);
                    } else {
                        this.addMessage('widdx', `❌ فشل في جلب الاتجاهات: ${result.error}`);
                    }
                } catch (error) {
                    this.hideTyping();
                    this.addMessage('widdx', '❌ حدث خطأ أثناء جلب الاتجاهات');
                }
            }
        }

        // Initialize the chat when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new WiddxChat();
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\widdx-ai\resources\views/chat.blade.php ENDPATH**/ ?>