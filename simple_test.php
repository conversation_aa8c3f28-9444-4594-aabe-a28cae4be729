<?php

echo "Testing Gemini Image Generation...\n";

$apiKey = 'AIzaSyAbPBjSTiK5s9LldpGotklEkXbaG0V65Rk';
$baseUrl = 'https://generativelanguage.googleapis.com';
$prompt = "A beautiful mountain landscape with snow-capped peaks";

$data = [
    'contents' => [
        [
            'parts' => [
                [
                    'text' => $prompt
                ]
            ]
        ]
    ],
    'generationConfig' => [
        'responseModalities' => ['TEXT', 'IMAGE'],
        'maxOutputTokens' => 8192,
        'temperature' => 0.8,
    ],
];

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => json_encode($data),
        'timeout' => 120,
    ]
]);

$url = $baseUrl . '/v1beta/models/gemini-2.0-flash-preview-image-generation:generateContent?key=' . $apiKey;

echo "Making request to: $url\n";
echo "Request data: " . json_encode($data, JSON_PRETTY_PRINT) . "\n\n";

$response = file_get_contents($url, false, $context);

if ($response === false) {
    echo "Error: Failed to make request\n";
    $error = error_get_last();
    echo "Error details: " . print_r($error, true) . "\n";
} else {
    echo "Success! Got response\n";
    echo "Response length: " . strlen($response) . " characters\n";
    
    $data = json_decode($response, true);
    if ($data === null) {
        echo "Error: Failed to parse JSON\n";
        echo "Raw response: " . substr($response, 0, 500) . "...\n";
    } else {
        echo "Response parsed successfully!\n";
        echo "Response keys: " . implode(', ', array_keys($data)) . "\n";
        
        if (isset($data['candidates'][0]['content']['parts'])) {
            echo "Found " . count($data['candidates'][0]['content']['parts']) . " parts\n";
            
            foreach ($data['candidates'][0]['content']['parts'] as $index => $part) {
                echo "Part $index: " . implode(', ', array_keys($part)) . "\n";
                
                if (isset($part['inlineData'])) {
                    echo "  - Image found! Size: " . strlen($part['inlineData']['data']) . " characters\n";
                    echo "  - MIME type: " . ($part['inlineData']['mimeType'] ?? 'unknown') . "\n";
                }
                
                if (isset($part['text'])) {
                    echo "  - Text: " . substr($part['text'], 0, 100) . "...\n";
                }
            }
        } else {
            echo "No parts found in response\n";
            echo "Full response: " . json_encode($data, JSON_PRETTY_PRINT) . "\n";
        }
    }
}
